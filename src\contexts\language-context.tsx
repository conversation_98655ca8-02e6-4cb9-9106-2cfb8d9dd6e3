'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

type Language = 'en' | 'zh'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Translation dictionary
const translations = {
  en: {
    // Header
    'header.title': 'Visual Template Editor',
    'header.import': 'Import Template',
    'header.save': 'Save Template',
    'header.export': 'Export Template',
    'header.undo': 'Undo',
    'header.redo': 'Redo',
    'header.help': 'Show Help Guide',
    'header.import': 'Import Template',
    'header.save': 'Save Template',
    'header.export': 'Export Template',
    'header.undo': 'Undo',
    'header.redo': 'Redo',
    'header.reset': 'Reset View',
    'header.zoomOut': 'Zoom Out',
    'header.zoomIn': 'Zoom In',
    'header.toggleGrid': 'Toggle Grid',
    'header.desktop': 'Desktop View',
    'header.tablet': 'Tablet View',
    'header.mobile': 'Mobile View',
    'header.preview': 'Preview',
    'header.exitPreview': 'Exit Preview',

    // Home Page
    'home.title': 'MediaWiki Template Builder',
    'home.subtitle': 'Create professional MediaWiki templates with visual drag-and-drop interface',
    'home.announcement': 'New: Figma-like Visual Canvas',
    'home.getStarted': 'Get Started',
    'home.heroTitle': 'Build Beautiful',
    'home.heroTitleHighlight': 'MediaWiki Templates',
    'home.heroDescription': 'Create stunning MediaWiki templates and CSS through an intuitive visual interface. Drag, drop, and design with real-time preview - no more wrestling with complex wikitext syntax.',
    'home.startCreating': 'Start Creating',
    'home.browseTemplates': 'Browse Templates',
    'home.openEditor': 'Open Editor',
    'home.quickStart': 'Quick Start',
    'home.visualCanvas': 'Visual Canvas',
    'home.visualCanvasDesc': 'Drag-and-drop interface for intuitive template building',
    'home.livePreview': 'Live Preview',
    'home.livePreviewDesc': 'See your templates rendered in real-time as you build',
    'home.oneClickExport': 'One-Click Export',
    'home.oneClickExportDesc': 'Export complete MediaWiki-ready packages instantly',
    'home.featuresTitle': 'Everything you need to create amazing templates',
    'home.featuresSubtitle': 'Professional-grade tools designed specifically for MediaWiki template creation',
    'home.visualDesigner': 'Visual Designer',
    'home.visualDesignerDesc': 'Drag-and-drop interface for building templates without writing raw wikitext.',
    'home.cssGeneration': 'CSS Generation',
    'home.cssGenerationDesc': 'Automatically generates clean, responsive CSS that works perfectly with MediaWiki.',
    'home.templateLibrary': 'Template Library',
    'home.templateLibraryDesc': 'Pre-built components for infoboxes, navigation boxes, and common patterns.',
    'home.ctaTitle': 'Ready to revolutionize your MediaWiki workflow?',
    'home.ctaSubtitle': 'Join wiki editors who have simplified their template creation process.',
    'home.footer': '© 2024 MediaWiki Template Builder. Built with ❤️ for the wiki community.',

    // Component Palette
    'palette.title': 'Components',
    'palette.subtitle': 'Drag components to the canvas to build your template',
    'palette.basicElements': 'Basic Elements',
    'palette.layout': 'Layout',
    'palette.mediawiki': 'MediaWiki',
    'palette.advanced': 'Advanced',
    'palette.quickActions': 'Quick Actions',
    'palette.importTemplate': '📋 Import Template',
    'palette.saveComponent': '💾 Save as Component',
    'palette.browseLibrary': '🚀 Browse Library',

    // Properties Panel
    'properties.title': 'Properties',
    'properties.noSelection': 'No Element Selected',
    'properties.noSelectionDesc': 'Select an element on the canvas to edit its properties',
    'properties.props': 'Props',
    'properties.style': 'Style',
    'properties.code': 'Code',
    'properties.general': 'General',
    'properties.elementType': 'Element Type',
    'properties.elementId': 'Element ID',
    'properties.visible': 'Visible',
    'properties.locked': 'Locked',
    'properties.positionSize': 'Position & Size',
    'properties.rotation': 'Rotation',
    'properties.textProperties': 'Text Properties',
    'properties.content': 'Content',
    'properties.fontSize': 'Font Size',
    'properties.color': 'Color',
    'properties.imageProperties': 'Image Properties',
    'properties.sourceUrl': 'Source URL',
    'properties.altText': 'Alt Text',
    'properties.appearance': 'Appearance',
    'properties.backgroundColor': 'Background Color',
    'properties.borderRadius': 'Border Radius',
    'properties.copy': 'Copy',
    'properties.export': 'Export',
    'properties.generatedWikitext': 'Generated Wikitext',
    'properties.copyCode': 'Copy Code',

    // Template Library
    'library.title': 'Template Library',
    'library.subtitle': 'Choose from professionally designed MediaWiki templates',
    'library.search': 'Search templates...',
    'library.mostPopular': 'Most Popular',
    'library.recentlyUpdated': 'Recently Updated',
    'library.highestRated': 'Highest Rated',
    'library.featuredOnly': 'Featured only',
    'library.categories': 'Categories',
    'library.allTemplates': 'All Templates',
    'library.infoboxes': 'Infoboxes',
    'library.navigation': 'Navigation',
    'library.citations': 'Citations',
    'library.tables': 'Tables',
    'library.layouts': 'Layouts',
    'library.specialized': 'Specialized',
    'library.noTemplates': 'No templates found',
    'library.noTemplatesDesc': 'Try adjusting your search or filters',
    'library.featured': 'Featured',
    'library.pro': 'Pro',
    'library.preview': 'Preview',
    'library.useTemplate': 'Use Template',

    // Canvas
    'canvas.startBuilding': 'Start Building Your Template',
    'canvas.startBuildingDesc': 'Drag components from the left panel to begin creating your MediaWiki template.',
    'canvas.noContent': 'No Content Yet',
    'canvas.noContentDesc': 'Exit preview mode to start building your template.',
    'canvas.selectTool': 'Select Tool',
    'canvas.textTool': 'Text Tool',
    'canvas.imageTool': 'Image Tool',
    'canvas.containerTool': 'Container Tool',
    'canvas.duplicate': 'Duplicate',
    'canvas.lock': 'Lock',
    'canvas.unlock': 'Unlock',
    'canvas.toggleVisibility': 'Toggle Visibility',
    'canvas.delete': 'Delete',

    // Common
    'common.close': 'Close',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Info'
  },
  zh: {
    // Header
    'header.title': '可视化模板编辑器',
    'header.import': '导入模板',
    'header.save': '保存模板',
    'header.export': '导出模板',
    'header.undo': '撤销',
    'header.redo': '重做',
    'header.help': '显示帮助指南',
    'header.import': '导入模板',
    'header.save': '保存模板',
    'header.export': '导出模板',
    'header.undo': '撤销',
    'header.redo': '重做',
    'header.reset': '重置视图',
    'header.zoomOut': '缩小',
    'header.zoomIn': '放大',
    'header.toggleGrid': '切换网格',
    'header.desktop': '桌面视图',
    'header.tablet': '平板视图',
    'header.mobile': '手机视图',
    'header.preview': '预览',
    'header.exitPreview': '退出预览',

    // Home Page
    'home.title': 'MediaWiki 模板构建器',
    'home.subtitle': '使用可视化拖拽界面创建专业的 MediaWiki 模板',
    'home.announcement': '新功能：类似 Figma 的可视化画布',
    'home.getStarted': '开始使用',
    'home.heroTitle': '构建精美的',
    'home.heroTitleHighlight': 'MediaWiki 模板',
    'home.heroDescription': '通过直观的可视化界面创建令人惊叹的 MediaWiki 模板和 CSS。拖拽、放置和设计，实时预览 - 不再与复杂的 wikitext 语法搏斗。',
    'home.startCreating': '开始创建',
    'home.browseTemplates': '浏览模板',
    'home.openEditor': '打开编辑器',
    'home.quickStart': '快速开始',
    'home.visualCanvas': '可视化画布',
    'home.visualCanvasDesc': '直观的拖拽界面，轻松构建模板',
    'home.livePreview': '实时预览',
    'home.livePreviewDesc': '在构建过程中实时查看模板渲染效果',
    'home.oneClickExport': '一键导出',
    'home.oneClickExportDesc': '即时导出完整的 MediaWiki 就绪包',
    'home.featuresTitle': '创建出色模板所需的一切',
    'home.featuresSubtitle': '专为 MediaWiki 模板创建设计的专业级工具',
    'home.visualDesigner': '可视化设计器',
    'home.visualDesignerDesc': '拖拽界面构建模板，无需编写原始 wikitext。',
    'home.cssGeneration': 'CSS 生成',
    'home.cssGenerationDesc': '自动生成与 MediaWiki 完美兼容的干净响应式 CSS。',
    'home.templateLibrary': '模板库',
    'home.templateLibraryDesc': '信息框、导航框和常见模式的预构建组件。',
    'home.ctaTitle': '准备好革新您的 MediaWiki 工作流程了吗？',
    'home.ctaSubtitle': '加入已经简化模板创建流程的 wiki 编辑者行列。',
    'home.footer': '© 2024 MediaWiki 模板构建器。为 wiki 社区用心打造。',

    // Component Palette
    'palette.title': '组件',
    'palette.subtitle': '将组件拖拽到画布上构建您的模板',
    'palette.basicElements': '基础元素',
    'palette.layout': '布局',
    'palette.mediawiki': 'MediaWiki',
    'palette.advanced': '高级',
    'palette.quickActions': '快速操作',
    'palette.importTemplate': '📋 导入模板',
    'palette.saveComponent': '💾 保存为组件',
    'palette.browseLibrary': '🚀 浏览库',

    // Properties Panel
    'properties.title': '属性',
    'properties.noSelection': '未选择元素',
    'properties.noSelectionDesc': '在画布上选择一个元素来编辑其属性',
    'properties.props': '属性',
    'properties.style': '样式',
    'properties.code': '代码',
    'properties.general': '常规',
    'properties.elementType': '元素类型',
    'properties.elementId': '元素ID',
    'properties.visible': '可见',
    'properties.locked': '锁定',
    'properties.positionSize': '位置和大小',
    'properties.rotation': '旋转',
    'properties.textProperties': '文本属性',
    'properties.content': '内容',
    'properties.fontSize': '字体大小',
    'properties.color': '颜色',
    'properties.imageProperties': '图片属性',
    'properties.sourceUrl': '源URL',
    'properties.altText': '替代文本',
    'properties.appearance': '外观',
    'properties.backgroundColor': '背景颜色',
    'properties.borderRadius': '边框圆角',
    'properties.copy': '复制',
    'properties.export': '导出',
    'properties.generatedWikitext': '生成的 Wikitext',
    'properties.copyCode': '复制代码',

    // Template Library
    'library.title': '模板库',
    'library.subtitle': '从专业设计的 MediaWiki 模板中选择',
    'library.search': '搜索模板...',
    'library.mostPopular': '最受欢迎',
    'library.recentlyUpdated': '最近更新',
    'library.highestRated': '评分最高',
    'library.featuredOnly': '仅精选',
    'library.categories': '分类',
    'library.allTemplates': '所有模板',
    'library.infoboxes': '信息框',
    'library.navigation': '导航',
    'library.citations': '引用',
    'library.tables': '表格',
    'library.layouts': '布局',
    'library.specialized': '专业',
    'library.noTemplates': '未找到模板',
    'library.noTemplatesDesc': '尝试调整您的搜索或筛选条件',
    'library.featured': '精选',
    'library.pro': '专业版',
    'library.preview': '预览',
    'library.useTemplate': '使用模板',

    // Canvas
    'canvas.startBuilding': '开始构建您的模板',
    'canvas.startBuildingDesc': '从左侧面板拖拽组件开始创建您的 MediaWiki 模板。',
    'canvas.noContent': '暂无内容',
    'canvas.noContentDesc': '退出预览模式开始构建您的模板。',
    'canvas.selectTool': '选择工具',
    'canvas.textTool': '文本工具',
    'canvas.imageTool': '图片工具',
    'canvas.containerTool': '容器工具',
    'canvas.duplicate': '复制',
    'canvas.lock': '锁定',
    'canvas.unlock': '解锁',
    'canvas.toggleVisibility': '切换可见性',
    'canvas.delete': '删除',

    // Common
    'common.close': '关闭',
    'common.cancel': '取消',
    'common.save': '保存',
    'common.delete': '删除',
    'common.edit': '编辑',
    'common.loading': '加载中...',
    'common.error': '错误',
    'common.success': '成功',
    'common.warning': '警告',
    'common.info': '信息'
  }
}

interface LanguageProviderProps {
  children: React.ReactNode
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en')

  useEffect(() => {
    // Load saved language preference
    const savedLanguage = localStorage.getItem('mediawiki-builder-language') as Language
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
      setLanguage(savedLanguage)
    }
  }, [])

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang)
    localStorage.setItem('mediawiki-builder-language', lang)
  }

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

export default LanguageProvider
