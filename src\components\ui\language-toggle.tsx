'use client'

import React from 'react'
import { Globe } from 'lucide-react'
import { useLanguage } from '@/contexts/language-context'

interface LanguageToggleProps {
  className?: string
}

export const LanguageToggle: React.FC<LanguageToggleProps> = ({ className = '' }) => {
  const { language, setLanguage } = useLanguage()

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'zh' : 'en')
  }

  return (
    <button
      onClick={toggleLanguage}
      className={`flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors ${className}`}
      title={language === 'en' ? 'Switch to Chinese' : '切换到英文'}
    >
      <Globe className="w-4 h-4 mr-2" />
      <span className="font-mono text-xs">
        {language === 'en' ? 'EN' : '中文'}
      </span>
    </button>
  )
}

export default LanguageToggle
