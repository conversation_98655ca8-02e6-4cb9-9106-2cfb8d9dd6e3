/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CMediawiki%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CMediawiki&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CMediawiki%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CMediawiki&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Mediawiki\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CMediawiki%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CMediawiki&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CMediawiki%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CMediawiki%5Csrc%5Ccomponents%5Cproviders%5Celectron-provider.tsx&modules=C%3A%5CMediawiki%5Csrc%5Ccontexts%5Clanguage-context.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CMediawiki%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CMediawiki%5Csrc%5Ccomponents%5Cproviders%5Celectron-provider.tsx&modules=C%3A%5CMediawiki%5Csrc%5Ccontexts%5Clanguage-context.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/electron-provider.tsx */ \"(ssr)/./src/components/providers/electron-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/language-context.tsx */ \"(ssr)/./src/contexts/language-context.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q01lZGlhd2lraSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNNZWRpYXdpa2klNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNNZWRpYXdpa2klNUNzcmMlNUNjb21wb25lbnRzJTVDcHJvdmlkZXJzJTVDZWxlY3Ryb24tcHJvdmlkZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q01lZGlhd2lraSU1Q3NyYyU1Q2NvbnRleHRzJTVDbGFuZ3VhZ2UtY29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRNQUFxRztBQUNyRyIsInNvdXJjZXMiOlsid2VicGFjazovL21lZGlhd2lraS10ZW1wbGF0ZS1idWlsZGVyLz9mZjAzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcTWVkaWF3aWtpXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxlbGVjdHJvbi1wcm92aWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXE1lZGlhd2lraVxcXFxzcmNcXFxcY29udGV4dHNcXFxcbGFuZ3VhZ2UtY29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CMediawiki%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CMediawiki%5Csrc%5Ccomponents%5Cproviders%5Celectron-provider.tsx&modules=C%3A%5CMediawiki%5Csrc%5Ccontexts%5Clanguage-context.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q01lZGlhd2lraSU1Q3NyYyU1Q2FwcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL21lZGlhd2lraS10ZW1wbGF0ZS1idWlsZGVyLz8yY2QzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcTWVkaWF3aWtpXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CMediawiki%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Globe,Layers,Palette,Sparkles,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_language_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/language-context */ \"(ssr)/./src/contexts/language-context.tsx\");\n/* harmony import */ var _components_ui_language_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/language-toggle */ \"(ssr)/./src/components/ui/language-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction HomePage() {\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { t } = (0,_contexts_language_context__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoaded(true);\n    }, []);\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading MediaWiki Template Builder...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 p-2 rounded-lg mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: t(\"home.title\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: t(\"home.subtitle\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_language_toggle__WEBPACK_IMPORTED_MODULE_4__.LanguageToggle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/editor\",\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center hover:shadow-lg hover:scale-105 transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t(\"home.getStarted\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates\",\n                                                className: \"bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md hover:scale-105 transform\",\n                                                children: \"My Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Now with Figma-like Visual Canvas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-900 sm:text-6xl mb-6\",\n                                children: [\n                                    t(\"home.heroTitle\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600\",\n                                        children: [\n                                            \" \",\n                                            t(\"home.heroTitleHighlight\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-10\",\n                                children: t(\"home.heroDescription\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4 mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/editor\",\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg px-8 py-4 rounded-lg transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, this),\n                                            t(\"home.startCreating\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/templates\",\n                                        className: \"bg-white hover:bg-gray-50 text-gray-900 font-semibold text-lg px-8 py-4 rounded-lg border-2 border-gray-200 hover:border-gray-300 transition-all duration-200 flex items-center hover:scale-105 transform\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this),\n                                            t(\"home.browseTemplates\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                children: \"Visual Canvas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Drag and drop elements on a Figma-like canvas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                children: \"Live Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"See your template render in real-time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                children: \"One-Click Export\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Export ready-to-use MediaWiki code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"Everything you need to create amazing templates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                        children: \"Professional-grade tools designed specifically for MediaWiki template creation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                children: \"Visual Designer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Drag-and-drop interface for building templates without writing raw wikitext.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                children: \"CSS Generation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Automatically generates clean, responsive CSS that works perfectly with MediaWiki.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                children: \"Template Library\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Pre-built components for infoboxes, navigation boxes, and common patterns.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-20 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-lg p-8 max-w-4xl mx-auto border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Ready to revolutionize your MediaWiki workflow?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 mb-8\",\n                                    children: \"Join wiki editors who have simplified their template creation process.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/editor\",\n                                    className: \"bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg px-8 py-3 rounded-lg transition-colors duration-200 inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Globe_Layers_Palette_Sparkles_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Get Started Now\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 MediaWiki Template Builder. Built with ❤️ for the wiki community.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/electron-provider.tsx":
/*!********************************************************!*\
  !*** ./src/components/providers/electron-provider.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ElectronProvider: () => (/* binding */ ElectronProvider),\n/* harmony export */   useElectron: () => (/* binding */ useElectron),\n/* harmony export */   useElectronFileOps: () => (/* binding */ useElectronFileOps),\n/* harmony export */   useElectronMenu: () => (/* binding */ useElectronMenu),\n/* harmony export */   usePlatform: () => (/* binding */ usePlatform)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useElectron,ElectronProvider,useElectronFileOps,useElectronMenu,usePlatform auto */ \n\nconst ElectronContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isElectron: false,\n    electronAPI: null,\n    platform: \"web\"\n});\nconst useElectron = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ElectronContext);\n    if (!context) {\n        throw new Error(\"useElectron must be used within an ElectronProvider\");\n    }\n    return context;\n};\nconst ElectronProvider = ({ children })=>{\n    const [isElectron, setIsElectron] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [electronAPI, setElectronAPI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"web\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if we're running in Electron\n        const checkElectron = ()=>{\n            if (false) {}\n        };\n        // Check immediately\n        checkElectron();\n        // Also check after a short delay in case the API loads asynchronously\n        const timer = setTimeout(checkElectron, 100);\n        return ()=>clearTimeout(timer);\n    }, []);\n    const contextValue = {\n        isElectron,\n        electronAPI,\n        platform\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElectronContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\providers\\\\electron-provider.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook for file operations\nconst useElectronFileOps = ()=>{\n    const { isElectron, electronAPI } = useElectron();\n    const saveTemplateFile = async (templateData)=>{\n        if (!isElectron || !electronAPI) {\n            throw new Error(\"File operations only available in Electron\");\n        }\n        return await electronAPI.saveTemplateFile(templateData);\n    };\n    const exportMediaWikiPackage = async (packageData)=>{\n        if (!isElectron || !electronAPI) {\n            throw new Error(\"Export operations only available in Electron\");\n        }\n        return await electronAPI.exportMediaWikiPackage(packageData);\n    };\n    const showSaveDialog = async (options)=>{\n        if (!isElectron || !electronAPI) {\n            throw new Error(\"Dialog operations only available in Electron\");\n        }\n        return await electronAPI.showSaveDialog(options);\n    };\n    const showOpenDialog = async (options)=>{\n        if (!isElectron || !electronAPI) {\n            throw new Error(\"Dialog operations only available in Electron\");\n        }\n        return await electronAPI.showOpenDialog(options);\n    };\n    const showNotification = async (title, body)=>{\n        if (!isElectron || !electronAPI) {\n            // Fallback to browser notification\n            if (\"Notification\" in window && Notification.permission === \"granted\") {\n                new Notification(title, {\n                    body\n                });\n                return {\n                    success: true\n                };\n            }\n            return {\n                success: false\n            };\n        }\n        return await electronAPI.showNotification(title, body);\n    };\n    return {\n        saveTemplateFile,\n        exportMediaWikiPackage,\n        showSaveDialog,\n        showOpenDialog,\n        showNotification,\n        isElectron\n    };\n};\n// Hook for menu integration\nconst useElectronMenu = ()=>{\n    const { isElectron, electronAPI } = useElectron();\n    const setupMenuHandlers = (handlers)=>{\n        if (!isElectron || !electronAPI) {\n            return ()=>{} // Return empty cleanup function\n            ;\n        }\n        // Set up menu event listeners\n        if (handlers.onNewTemplate) {\n            electronAPI.onMenuNewTemplate(handlers.onNewTemplate);\n        }\n        if (handlers.onOpenTemplate) {\n            electronAPI.onMenuOpenTemplate((event, templateData)=>{\n                handlers.onOpenTemplate?.(templateData);\n            });\n        }\n        if (handlers.onSaveTemplate) {\n            electronAPI.onMenuSaveTemplate(handlers.onSaveTemplate);\n        }\n        if (handlers.onExportPackage) {\n            electronAPI.onMenuExportPackage(handlers.onExportPackage);\n        }\n        // Return cleanup function\n        return ()=>{\n            if (electronAPI) {\n                electronAPI.removeAllListeners(\"menu-new-template\");\n                electronAPI.removeAllListeners(\"menu-open-template\");\n                electronAPI.removeAllListeners(\"menu-save-template\");\n                electronAPI.removeAllListeners(\"menu-export-package\");\n            }\n        };\n    };\n    return {\n        setupMenuHandlers,\n        isElectron\n    };\n};\n// Hook for platform-specific behavior\nconst usePlatform = ()=>{\n    const { platform, isElectron } = useElectron();\n    const isMac = platform === \"darwin\";\n    const isWindows = platform === \"win32\";\n    const isLinux = platform === \"linux\";\n    const isWeb = !isElectron;\n    const getKeyboardShortcut = (shortcut)=>{\n        if (isMac) {\n            return shortcut.replace(\"Ctrl\", \"Cmd\");\n        }\n        return shortcut;\n    };\n    return {\n        platform,\n        isMac,\n        isWindows,\n        isLinux,\n        isWeb,\n        isElectron,\n        getKeyboardShortcut\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/electron-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/language-toggle.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/language-toggle.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageToggle: () => (/* binding */ LanguageToggle),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _contexts_language_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/language-context */ \"(ssr)/./src/contexts/language-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ LanguageToggle,default auto */ \n\n\n\nconst LanguageToggle = ({ className = \"\" })=>{\n    const { language, setLanguage } = (0,_contexts_language_context__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const toggleLanguage = ()=>{\n        setLanguage(language === \"en\" ? \"zh\" : \"en\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleLanguage,\n        className: `flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors ${className}`,\n        title: language === \"en\" ? \"Switch to Chinese\" : \"切换到英文\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\ui\\\\language-toggle.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-mono text-xs\",\n                children: language === \"en\" ? \"EN\" : \"中文\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\ui\\\\language-toggle.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\ui\\\\language-toggle.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageToggle);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/language-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/language-context.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/language-context.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage,default auto */ \n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Translation dictionary\nconst translations = {\n    en: {\n        // Header\n        \"header.title\": \"Visual Template Editor\",\n        \"header.import\": \"Import Template\",\n        \"header.save\": \"Save Template\",\n        \"header.export\": \"Export Template\",\n        \"header.undo\": \"Undo\",\n        \"header.redo\": \"Redo\",\n        \"header.help\": \"Show Help Guide\",\n        \"header.import\": \"Import Template\",\n        \"header.save\": \"Save Template\",\n        \"header.export\": \"Export Template\",\n        \"header.undo\": \"Undo\",\n        \"header.redo\": \"Redo\",\n        \"header.reset\": \"Reset View\",\n        \"header.zoomOut\": \"Zoom Out\",\n        \"header.zoomIn\": \"Zoom In\",\n        \"header.toggleGrid\": \"Toggle Grid\",\n        \"header.desktop\": \"Desktop View\",\n        \"header.tablet\": \"Tablet View\",\n        \"header.mobile\": \"Mobile View\",\n        \"header.preview\": \"Preview\",\n        \"header.exitPreview\": \"Exit Preview\",\n        // Home Page\n        \"home.title\": \"MediaWiki Template Builder\",\n        \"home.subtitle\": \"Create professional MediaWiki templates with visual drag-and-drop interface\",\n        \"home.announcement\": \"New: Figma-like Visual Canvas\",\n        \"home.getStarted\": \"Get Started\",\n        \"home.heroTitle\": \"Build Beautiful\",\n        \"home.heroTitleHighlight\": \"MediaWiki Templates\",\n        \"home.heroDescription\": \"Create stunning MediaWiki templates and CSS through an intuitive visual interface. Drag, drop, and design with real-time preview - no more wrestling with complex wikitext syntax.\",\n        \"home.startCreating\": \"Start Creating\",\n        \"home.browseTemplates\": \"Browse Templates\",\n        \"home.openEditor\": \"Open Editor\",\n        \"home.quickStart\": \"Quick Start\",\n        \"home.visualCanvas\": \"Visual Canvas\",\n        \"home.visualCanvasDesc\": \"Drag-and-drop interface for intuitive template building\",\n        \"home.livePreview\": \"Live Preview\",\n        \"home.livePreviewDesc\": \"See your templates rendered in real-time as you build\",\n        \"home.oneClickExport\": \"One-Click Export\",\n        \"home.oneClickExportDesc\": \"Export complete MediaWiki-ready packages instantly\",\n        \"home.featuresTitle\": \"Everything you need to create amazing templates\",\n        \"home.featuresSubtitle\": \"Professional-grade tools designed specifically for MediaWiki template creation\",\n        \"home.visualDesigner\": \"Visual Designer\",\n        \"home.visualDesignerDesc\": \"Drag-and-drop interface for building templates without writing raw wikitext.\",\n        \"home.cssGeneration\": \"CSS Generation\",\n        \"home.cssGenerationDesc\": \"Automatically generates clean, responsive CSS that works perfectly with MediaWiki.\",\n        \"home.templateLibrary\": \"Template Library\",\n        \"home.templateLibraryDesc\": \"Pre-built components for infoboxes, navigation boxes, and common patterns.\",\n        \"home.ctaTitle\": \"Ready to revolutionize your MediaWiki workflow?\",\n        \"home.ctaSubtitle\": \"Join wiki editors who have simplified their template creation process.\",\n        \"home.footer\": \"\\xa9 2024 MediaWiki Template Builder. Built with ❤️ for the wiki community.\",\n        // Component Palette\n        \"palette.title\": \"Components\",\n        \"palette.subtitle\": \"Drag components to the canvas to build your template\",\n        \"palette.basicElements\": \"Basic Elements\",\n        \"palette.layout\": \"Layout\",\n        \"palette.mediawiki\": \"MediaWiki\",\n        \"palette.advanced\": \"Advanced\",\n        \"palette.quickActions\": \"Quick Actions\",\n        \"palette.importTemplate\": \"\\uD83D\\uDCCB Import Template\",\n        \"palette.saveComponent\": \"\\uD83D\\uDCBE Save as Component\",\n        \"palette.browseLibrary\": \"\\uD83D\\uDE80 Browse Library\",\n        // Properties Panel\n        \"properties.title\": \"Properties\",\n        \"properties.noSelection\": \"No Element Selected\",\n        \"properties.noSelectionDesc\": \"Select an element on the canvas to edit its properties\",\n        \"properties.props\": \"Props\",\n        \"properties.style\": \"Style\",\n        \"properties.code\": \"Code\",\n        \"properties.general\": \"General\",\n        \"properties.elementType\": \"Element Type\",\n        \"properties.elementId\": \"Element ID\",\n        \"properties.visible\": \"Visible\",\n        \"properties.locked\": \"Locked\",\n        \"properties.positionSize\": \"Position & Size\",\n        \"properties.rotation\": \"Rotation\",\n        \"properties.textProperties\": \"Text Properties\",\n        \"properties.content\": \"Content\",\n        \"properties.fontSize\": \"Font Size\",\n        \"properties.color\": \"Color\",\n        \"properties.imageProperties\": \"Image Properties\",\n        \"properties.sourceUrl\": \"Source URL\",\n        \"properties.altText\": \"Alt Text\",\n        \"properties.appearance\": \"Appearance\",\n        \"properties.backgroundColor\": \"Background Color\",\n        \"properties.borderRadius\": \"Border Radius\",\n        \"properties.copy\": \"Copy\",\n        \"properties.export\": \"Export\",\n        \"properties.generatedWikitext\": \"Generated Wikitext\",\n        \"properties.copyCode\": \"Copy Code\",\n        // Template Library\n        \"library.title\": \"Template Library\",\n        \"library.subtitle\": \"Choose from professionally designed MediaWiki templates\",\n        \"library.search\": \"Search templates...\",\n        \"library.mostPopular\": \"Most Popular\",\n        \"library.recentlyUpdated\": \"Recently Updated\",\n        \"library.highestRated\": \"Highest Rated\",\n        \"library.featuredOnly\": \"Featured only\",\n        \"library.categories\": \"Categories\",\n        \"library.allTemplates\": \"All Templates\",\n        \"library.infoboxes\": \"Infoboxes\",\n        \"library.navigation\": \"Navigation\",\n        \"library.citations\": \"Citations\",\n        \"library.tables\": \"Tables\",\n        \"library.layouts\": \"Layouts\",\n        \"library.specialized\": \"Specialized\",\n        \"library.noTemplates\": \"No templates found\",\n        \"library.noTemplatesDesc\": \"Try adjusting your search or filters\",\n        \"library.featured\": \"Featured\",\n        \"library.pro\": \"Pro\",\n        \"library.preview\": \"Preview\",\n        \"library.useTemplate\": \"Use Template\",\n        // Canvas\n        \"canvas.startBuilding\": \"Start Building Your Template\",\n        \"canvas.startBuildingDesc\": \"Drag components from the left panel to begin creating your MediaWiki template.\",\n        \"canvas.noContent\": \"No Content Yet\",\n        \"canvas.noContentDesc\": \"Exit preview mode to start building your template.\",\n        \"canvas.selectTool\": \"Select Tool\",\n        \"canvas.textTool\": \"Text Tool\",\n        \"canvas.imageTool\": \"Image Tool\",\n        \"canvas.containerTool\": \"Container Tool\",\n        \"canvas.duplicate\": \"Duplicate\",\n        \"canvas.lock\": \"Lock\",\n        \"canvas.unlock\": \"Unlock\",\n        \"canvas.toggleVisibility\": \"Toggle Visibility\",\n        \"canvas.delete\": \"Delete\",\n        // Common\n        \"common.close\": \"Close\",\n        \"common.cancel\": \"Cancel\",\n        \"common.save\": \"Save\",\n        \"common.delete\": \"Delete\",\n        \"common.edit\": \"Edit\",\n        \"common.loading\": \"Loading...\",\n        \"common.error\": \"Error\",\n        \"common.success\": \"Success\",\n        \"common.warning\": \"Warning\",\n        \"common.info\": \"Info\"\n    },\n    zh: {\n        // Header\n        \"header.title\": \"可视化模板编辑器\",\n        \"header.import\": \"导入模板\",\n        \"header.save\": \"保存模板\",\n        \"header.export\": \"导出模板\",\n        \"header.undo\": \"撤销\",\n        \"header.redo\": \"重做\",\n        \"header.help\": \"显示帮助指南\",\n        \"header.import\": \"导入模板\",\n        \"header.save\": \"保存模板\",\n        \"header.export\": \"导出模板\",\n        \"header.undo\": \"撤销\",\n        \"header.redo\": \"重做\",\n        \"header.reset\": \"重置视图\",\n        \"header.zoomOut\": \"缩小\",\n        \"header.zoomIn\": \"放大\",\n        \"header.toggleGrid\": \"切换网格\",\n        \"header.desktop\": \"桌面视图\",\n        \"header.tablet\": \"平板视图\",\n        \"header.mobile\": \"手机视图\",\n        \"header.preview\": \"预览\",\n        \"header.exitPreview\": \"退出预览\",\n        // Home Page\n        \"home.title\": \"MediaWiki 模板构建器\",\n        \"home.subtitle\": \"使用可视化拖拽界面创建专业的 MediaWiki 模板\",\n        \"home.announcement\": \"新功能：类似 Figma 的可视化画布\",\n        \"home.getStarted\": \"开始使用\",\n        \"home.heroTitle\": \"构建精美的\",\n        \"home.heroTitleHighlight\": \"MediaWiki 模板\",\n        \"home.heroDescription\": \"通过直观的可视化界面创建令人惊叹的 MediaWiki 模板和 CSS。拖拽、放置和设计，实时预览 - 不再与复杂的 wikitext 语法搏斗。\",\n        \"home.startCreating\": \"开始创建\",\n        \"home.browseTemplates\": \"浏览模板\",\n        \"home.openEditor\": \"打开编辑器\",\n        \"home.quickStart\": \"快速开始\",\n        \"home.visualCanvas\": \"可视化画布\",\n        \"home.visualCanvasDesc\": \"直观的拖拽界面，轻松构建模板\",\n        \"home.livePreview\": \"实时预览\",\n        \"home.livePreviewDesc\": \"在构建过程中实时查看模板渲染效果\",\n        \"home.oneClickExport\": \"一键导出\",\n        \"home.oneClickExportDesc\": \"即时导出完整的 MediaWiki 就绪包\",\n        \"home.featuresTitle\": \"创建出色模板所需的一切\",\n        \"home.featuresSubtitle\": \"专为 MediaWiki 模板创建设计的专业级工具\",\n        \"home.visualDesigner\": \"可视化设计器\",\n        \"home.visualDesignerDesc\": \"拖拽界面构建模板，无需编写原始 wikitext。\",\n        \"home.cssGeneration\": \"CSS 生成\",\n        \"home.cssGenerationDesc\": \"自动生成与 MediaWiki 完美兼容的干净响应式 CSS。\",\n        \"home.templateLibrary\": \"模板库\",\n        \"home.templateLibraryDesc\": \"信息框、导航框和常见模式的预构建组件。\",\n        \"home.ctaTitle\": \"准备好革新您的 MediaWiki 工作流程了吗？\",\n        \"home.ctaSubtitle\": \"加入已经简化模板创建流程的 wiki 编辑者行列。\",\n        \"home.footer\": \"\\xa9 2024 MediaWiki 模板构建器。为 wiki 社区用心打造。\",\n        // Component Palette\n        \"palette.title\": \"组件\",\n        \"palette.subtitle\": \"将组件拖拽到画布上构建您的模板\",\n        \"palette.basicElements\": \"基础元素\",\n        \"palette.layout\": \"布局\",\n        \"palette.mediawiki\": \"MediaWiki\",\n        \"palette.advanced\": \"高级\",\n        \"palette.quickActions\": \"快速操作\",\n        \"palette.importTemplate\": \"\\uD83D\\uDCCB 导入模板\",\n        \"palette.saveComponent\": \"\\uD83D\\uDCBE 保存为组件\",\n        \"palette.browseLibrary\": \"\\uD83D\\uDE80 浏览库\",\n        // Properties Panel\n        \"properties.title\": \"属性\",\n        \"properties.noSelection\": \"未选择元素\",\n        \"properties.noSelectionDesc\": \"在画布上选择一个元素来编辑其属性\",\n        \"properties.props\": \"属性\",\n        \"properties.style\": \"样式\",\n        \"properties.code\": \"代码\",\n        \"properties.general\": \"常规\",\n        \"properties.elementType\": \"元素类型\",\n        \"properties.elementId\": \"元素ID\",\n        \"properties.visible\": \"可见\",\n        \"properties.locked\": \"锁定\",\n        \"properties.positionSize\": \"位置和大小\",\n        \"properties.rotation\": \"旋转\",\n        \"properties.textProperties\": \"文本属性\",\n        \"properties.content\": \"内容\",\n        \"properties.fontSize\": \"字体大小\",\n        \"properties.color\": \"颜色\",\n        \"properties.imageProperties\": \"图片属性\",\n        \"properties.sourceUrl\": \"源URL\",\n        \"properties.altText\": \"替代文本\",\n        \"properties.appearance\": \"外观\",\n        \"properties.backgroundColor\": \"背景颜色\",\n        \"properties.borderRadius\": \"边框圆角\",\n        \"properties.copy\": \"复制\",\n        \"properties.export\": \"导出\",\n        \"properties.generatedWikitext\": \"生成的 Wikitext\",\n        \"properties.copyCode\": \"复制代码\",\n        // Template Library\n        \"library.title\": \"模板库\",\n        \"library.subtitle\": \"从专业设计的 MediaWiki 模板中选择\",\n        \"library.search\": \"搜索模板...\",\n        \"library.mostPopular\": \"最受欢迎\",\n        \"library.recentlyUpdated\": \"最近更新\",\n        \"library.highestRated\": \"评分最高\",\n        \"library.featuredOnly\": \"仅精选\",\n        \"library.categories\": \"分类\",\n        \"library.allTemplates\": \"所有模板\",\n        \"library.infoboxes\": \"信息框\",\n        \"library.navigation\": \"导航\",\n        \"library.citations\": \"引用\",\n        \"library.tables\": \"表格\",\n        \"library.layouts\": \"布局\",\n        \"library.specialized\": \"专业\",\n        \"library.noTemplates\": \"未找到模板\",\n        \"library.noTemplatesDesc\": \"尝试调整您的搜索或筛选条件\",\n        \"library.featured\": \"精选\",\n        \"library.pro\": \"专业版\",\n        \"library.preview\": \"预览\",\n        \"library.useTemplate\": \"使用模板\",\n        // Canvas\n        \"canvas.startBuilding\": \"开始构建您的模板\",\n        \"canvas.startBuildingDesc\": \"从左侧面板拖拽组件开始创建您的 MediaWiki 模板。\",\n        \"canvas.noContent\": \"暂无内容\",\n        \"canvas.noContentDesc\": \"退出预览模式开始构建您的模板。\",\n        \"canvas.selectTool\": \"选择工具\",\n        \"canvas.textTool\": \"文本工具\",\n        \"canvas.imageTool\": \"图片工具\",\n        \"canvas.containerTool\": \"容器工具\",\n        \"canvas.duplicate\": \"复制\",\n        \"canvas.lock\": \"锁定\",\n        \"canvas.unlock\": \"解锁\",\n        \"canvas.toggleVisibility\": \"切换可见性\",\n        \"canvas.delete\": \"删除\",\n        // Common\n        \"common.close\": \"关闭\",\n        \"common.cancel\": \"取消\",\n        \"common.save\": \"保存\",\n        \"common.delete\": \"删除\",\n        \"common.edit\": \"编辑\",\n        \"common.loading\": \"加载中...\",\n        \"common.error\": \"错误\",\n        \"common.success\": \"成功\",\n        \"common.warning\": \"警告\",\n        \"common.info\": \"信息\"\n    }\n};\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load saved language preference\n        const savedLanguage = localStorage.getItem(\"mediawiki-builder-language\");\n        if (savedLanguage && (savedLanguage === \"en\" || savedLanguage === \"zh\")) {\n            setLanguage(savedLanguage);\n        }\n    }, []);\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        localStorage.setItem(\"mediawiki-builder-language\", lang);\n    };\n    const t = (key)=>{\n        return translations[language][key] || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage: handleSetLanguage,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\contexts\\\\language-context.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/language-context.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"47753c56857c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWVkaWF3aWtpLXRlbXBsYXRlLWJ1aWxkZXIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2M2OTYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0Nzc1M2M1Njg1N2NcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_language_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/language-context */ \"(rsc)/./src/contexts/language-context.tsx\");\n/* harmony import */ var _components_providers_electron_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/electron-provider */ \"(rsc)/./src/components/providers/electron-provider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"MediaWiki Template Builder\",\n    description: \"A visual tool for creating beautiful MediaWiki templates and CSS through drag-and-drop interface\",\n    keywords: [\n        \"mediawiki\",\n        \"templates\",\n        \"visual-editor\",\n        \"wikitext\",\n        \"css-generator\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_language_context__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_electron_provider__WEBPACK_IMPORTED_MODULE_3__.ElectronProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/providers/electron-provider.tsx":
/*!********************************************************!*\
  !*** ./src/components/providers/electron-provider.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ElectronProvider: () => (/* binding */ e1),
/* harmony export */   useElectron: () => (/* binding */ e0),
/* harmony export */   useElectronFileOps: () => (/* binding */ e2),
/* harmony export */   useElectronMenu: () => (/* binding */ e3),
/* harmony export */   usePlatform: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\components\providers\electron-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\components\providers\electron-provider.tsx#useElectron`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\components\providers\electron-provider.tsx#ElectronProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\components\providers\electron-provider.tsx#useElectronFileOps`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\components\providers\electron-provider.tsx#useElectronMenu`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\components\providers\electron-provider.tsx#usePlatform`);


/***/ }),

/***/ "(rsc)/./src/contexts/language-context.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/language-context.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\contexts\language-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\contexts\language-context.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Mediawiki\src\contexts\language-context.tsx#useLanguage`);


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CMediawiki%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CMediawiki&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();