'use client'

import Link from 'next/link'
import { FileText, Palette, Eye, Download, Upload, Zap, Globe, Layers, Sparkles, ArrowRight } from 'lucide-react'
import { useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/language-context'
import { LanguageToggle } from '@/components/ui/language-toggle'

export default function HomePage() {
  const [isLoaded, setIsLoaded] = useState(false)
  const { t } = useLanguage()

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading MediaWiki Template Builder...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="bg-blue-600 p-2 rounded-lg mr-3">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {t('home.title')}
                </h1>
                <p className="text-sm text-gray-600">{t('home.subtitle')}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <LanguageToggle />
              <nav className="flex space-x-3">
                <Link
                  href="/editor"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center hover:shadow-lg hover:scale-105 transform"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  {t('home.getStarted')}
                </Link>
                <Link
                  href="/templates"
                  className="bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md hover:scale-105 transform"
                >
                  My Templates
                </Link>
              </nav>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4">
              <Globe className="w-4 h-4 mr-2" />
              Now with Figma-like Visual Canvas
            </div>
          </div>

          <h2 className="text-4xl font-bold text-gray-900 sm:text-6xl mb-6">
            {t('home.heroTitle')}
            <span className="text-blue-600"> {t('home.heroTitleHighlight')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-10">
            {t('home.heroDescription')}
          </p>

          <div className="flex justify-center gap-4 mb-12">
            <Link
              href="/editor"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg px-8 py-4 rounded-lg transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1 hover:scale-105"
            >
              <Zap className="w-5 h-5 mr-2" />
              {t('home.startCreating')}
            </Link>
            <Link
              href="/templates"
              className="bg-white hover:bg-gray-50 text-gray-900 font-semibold text-lg px-8 py-4 rounded-lg border-2 border-gray-200 hover:border-gray-300 transition-all duration-200 flex items-center hover:scale-105 transform"
            >
              <Eye className="w-5 h-5 mr-2" />
              {t('home.browseTemplates')}
            </Link>
          </div>

          {/* Quick Start Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Layers className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Visual Canvas</h3>
              <p className="text-sm text-gray-600">Drag and drop elements on a Figma-like canvas</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Live Preview</h3>
              <p className="text-sm text-gray-600">See your template render in real-time</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Download className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">One-Click Export</h3>
              <p className="text-sm text-gray-600">Export ready-to-use MediaWiki code</p>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Everything you need to create amazing templates
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Professional-grade tools designed specifically for MediaWiki template creation
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">
                Visual Designer
              </h4>
              <p className="text-gray-600">
                Drag-and-drop interface for building templates without writing raw wikitext.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Palette className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">
                CSS Generation
              </h4>
              <p className="text-gray-600">
                Automatically generates clean, responsive CSS that works perfectly with MediaWiki.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Upload className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">
                Template Library
              </h4>
              <p className="text-gray-600">
                Pre-built components for infoboxes, navigation boxes, and common patterns.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <div className="bg-white rounded-2xl shadow-lg p-8 max-w-4xl mx-auto border border-gray-200">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to revolutionize your MediaWiki workflow?
            </h3>
            <p className="text-xl text-gray-600 mb-8">
              Join wiki editors who have simplified their template creation process.
            </p>
            <Link
              href="/editor"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg px-8 py-3 rounded-lg transition-colors duration-200 inline-flex items-center"
            >
              <Sparkles className="w-5 h-5 mr-2" />
              Get Started Now
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 MediaWiki Template Builder. Built with ❤️ for the wiki community.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
