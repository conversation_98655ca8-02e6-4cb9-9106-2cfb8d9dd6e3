'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { 
  Move, 
  RotateCcw, 
  Co<PERSON>, 
  Trash2, 
  <PERSON>, 
  Unlock, 
  <PERSON>, 
  <PERSON>Off,
  MousePointer,
  Square,
  Type,
  Image as ImageIcon,
  Layers
} from 'lucide-react'

interface CanvasElement {
  id: string
  type: 'text' | 'image' | 'container' | 'table' | 'infobox'
  x: number
  y: number
  width: number
  height: number
  rotation: number
  locked: boolean
  visible: boolean
  properties: Record<string, any>
  children?: CanvasElement[]
}

interface VisualCanvasProps {
  elements: CanvasElement[]
  selectedElementId: string | null
  onElementsChange: (elements: CanvasElement[]) => void
  onElementSelect: (elementId: string | null) => void
  showGrid: boolean
  zoom: number
}

export const VisualCanvas: React.FC<VisualCanvasProps> = ({
  elements,
  selectedElementId,
  onElementsChange,
  onElementSelect,
  showGrid,
  zoom
}) => {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [isResizing, setIsResizing] = useState(false)
  const [resizeHandle, setResizeHandle] = useState<string>('')
  const [tool, setTool] = useState<'select' | 'text' | 'image' | 'container'>('select')

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      onElementSelect(null)
    }
  }, [onElementSelect])

  const handleCanvasDoubleClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      const rect = canvasRef.current!.getBoundingClientRect()
      const x = (e.clientX - rect.left) / zoom
      const y = (e.clientY - rect.top) / zoom

      const newElement: CanvasElement = {
        id: `text-${Date.now()}`,
        type: 'text',
        x: x - 50, // Center the element
        y: y - 15,
        width: 100,
        height: 30,
        rotation: 0,
        locked: false,
        visible: true,
        properties: {
          content: '新文本',
          fontSize: 14,
          fontWeight: 'normal',
          color: '#000000',
          textAlign: 'left'
        }
      }

      onElementsChange([...elements, newElement])
      onElementSelect(newElement.id)
    }
  }, [elements, onElementsChange, onElementSelect, zoom])

  const handleElementClick = useCallback((e: React.MouseEvent, elementId: string) => {
    e.stopPropagation()
    onElementSelect(elementId)
  }, [onElementSelect])

  const handleMouseDown = useCallback((e: React.MouseEvent, elementId: string) => {
    if (tool !== 'select') return
    
    const element = elements.find(el => el.id === elementId)
    if (!element || element.locked) return

    setIsDragging(true)
    setDragStart({ x: e.clientX, y: e.clientY })
    setDragOffset({ x: element.x, y: element.y })
    onElementSelect(elementId)
  }, [tool, elements, onElementSelect])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !selectedElementId) return

    const deltaX = e.clientX - dragStart.x
    const deltaY = e.clientY - dragStart.y

    const updatedElements = elements.map(el => {
      if (el.id === selectedElementId) {
        return {
          ...el,
          x: dragOffset.x + deltaX / zoom,
          y: dragOffset.y + deltaY / zoom
        }
      }
      return el
    })

    onElementsChange(updatedElements)
  }, [isDragging, selectedElementId, dragStart, dragOffset, elements, onElementsChange, zoom])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
    setIsResizing(false)
    setResizeHandle('')
  }, [])

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'))
      const rect = canvasRef.current?.getBoundingClientRect()
      
      if (!rect) return

      const x = (e.clientX - rect.left) / zoom
      const y = (e.clientY - rect.top) / zoom

      const newElement: CanvasElement = {
        id: `element-${Date.now()}`,
        type: data.type,
        x,
        y,
        width: data.type === 'text' ? 200 : 150,
        height: data.type === 'text' ? 40 : 100,
        rotation: 0,
        locked: false,
        visible: true,
        properties: getDefaultProperties(data.type)
      }

      onElementsChange([...elements, newElement])
      onElementSelect(newElement.id)
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }, [elements, onElementsChange, onElementSelect, zoom])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
  }, [])

  const duplicateElement = useCallback((elementId: string) => {
    const element = elements.find(el => el.id === elementId)
    if (!element) return

    const newElement: CanvasElement = {
      ...element,
      id: `element-${Date.now()}`,
      x: element.x + 20,
      y: element.y + 20
    }

    onElementsChange([...elements, newElement])
    onElementSelect(newElement.id)
  }, [elements, onElementsChange, onElementSelect])

  const deleteElement = useCallback((elementId: string) => {
    const updatedElements = elements.filter(el => el.id !== elementId)
    onElementsChange(updatedElements)
    if (selectedElementId === elementId) {
      onElementSelect(null)
    }
  }, [elements, onElementsChange, selectedElementId, onElementSelect])

  const toggleElementLock = useCallback((elementId: string) => {
    const updatedElements = elements.map(el => {
      if (el.id === elementId) {
        return { ...el, locked: !el.locked }
      }
      return el
    })
    onElementsChange(updatedElements)
  }, [elements, onElementsChange])

  const toggleElementVisibility = useCallback((elementId: string) => {
    const updatedElements = elements.map(el => {
      if (el.id === elementId) {
        return { ...el, visible: !el.visible }
      }
      return el
    })
    onElementsChange(updatedElements)
  }, [elements, onElementsChange])

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Canvas Toolbar */}
      <div className="absolute top-4 left-4 z-10 bg-white rounded-lg shadow-lg border border-gray-200 p-2 flex items-center space-x-1">
        <div className="flex items-center space-x-1 pr-2 border-r border-gray-200">
          <button
            onClick={() => setTool('select')}
            className={`p-2 rounded ${tool === 'select' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            title="选择工具 (S)"
          >
            <MousePointer className="w-4 h-4" />
          </button>
          <button
            onClick={() => setTool('text')}
            className={`p-2 rounded ${tool === 'text' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            title="文本工具 (T) - 双击画布快速添加"
          >
            <Type className="w-4 h-4" />
          </button>
          <button
            onClick={() => setTool('image')}
            className={`p-2 rounded ${tool === 'image' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            title="图片工具 (I)"
          >
            <ImageIcon className="w-4 h-4" />
          </button>
          <button
            onClick={() => setTool('container')}
            className={`p-2 rounded ${tool === 'container' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            title="容器工具 (C)"
          >
            <Square className="w-4 h-4" />
          </button>
        </div>

        <div className="text-xs text-gray-500 px-2">
          {elements.length === 0 ? '双击画布添加文本' : `${elements.length} 个元素`}
        </div>
      </div>

      {/* Canvas */}
      <div
        ref={canvasRef}
        className="w-full h-full relative cursor-crosshair bg-white border border-gray-200 rounded-lg shadow-sm"
        onClick={handleCanvasClick}
        onDoubleClick={handleCanvasDoubleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        style={{ transform: `scale(${zoom})`, transformOrigin: 'top left', minHeight: '600px' }}
      >
        {/* Grid Background */}
        {showGrid && (
          <div 
            className="absolute inset-0 opacity-20 pointer-events-none"
            style={{
              backgroundImage: `
                linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />
        )}

        {/* Canvas Elements */}
        {elements.length === 0 ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-400 max-w-md">
              <Layers className="w-20 h-20 mx-auto mb-6 text-gray-300" />
              <h3 className="text-xl font-semibold mb-4 text-gray-600">开始创建您的模板</h3>
              <div className="space-y-3 text-sm text-gray-500">
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span>从左侧组件面板拖拽元素到画布</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>使用顶部工具栏快速添加元素</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                  <span>点击"模板库"按钮选择预制模板</span>
                </div>
              </div>
              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-xs text-blue-600">💡 提示：双击画布任意位置快速添加文本元素</p>
              </div>
            </div>
          </div>
        ) : (
          elements.map(element => (
            <CanvasElementComponent
              key={element.id}
              element={element}
              isSelected={selectedElementId === element.id}
              onMouseDown={(e) => handleMouseDown(e, element.id)}
              onClick={(e) => handleElementClick(e, element.id)}
              onDuplicate={() => duplicateElement(element.id)}
              onDelete={() => deleteElement(element.id)}
              onToggleLock={() => toggleElementLock(element.id)}
              onToggleVisibility={() => toggleElementVisibility(element.id)}
            />
          ))
        )}
      </div>
    </div>
  )
}

interface CanvasElementComponentProps {
  element: CanvasElement
  isSelected: boolean
  onMouseDown: (e: React.MouseEvent) => void
  onClick: (e: React.MouseEvent) => void
  onDuplicate: () => void
  onDelete: () => void
  onToggleLock: () => void
  onToggleVisibility: () => void
}

const CanvasElementComponent: React.FC<CanvasElementComponentProps> = ({
  element,
  isSelected,
  onMouseDown,
  onClick,
  onDuplicate,
  onDelete,
  onToggleLock,
  onToggleVisibility
}) => {
  if (!element.visible) return null

  const elementStyle: React.CSSProperties = {
    position: 'absolute',
    left: element.x,
    top: element.y,
    width: element.width,
    height: element.height,
    transform: `rotate(${element.rotation}deg)`,
    cursor: element.locked ? 'not-allowed' : 'move',
    border: isSelected ? '2px solid #3b82f6' : '1px solid transparent',
    borderRadius: '4px'
  }

  return (
    <div
      style={elementStyle}
      onMouseDown={onMouseDown}
      onClick={onClick}
      className={`
        ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
        ${element.locked ? 'opacity-60' : ''}
        hover:border-gray-300 transition-colors
      `}
    >
      {/* Element Content */}
      <div className="w-full h-full relative">
        {renderElementContent(element)}
        
        {/* Selection Controls */}
        {isSelected && (
          <div className="absolute -top-8 left-0 flex items-center space-x-1 bg-white rounded shadow-lg border border-gray-200 px-2 py-1">
            <button
              onClick={(e) => { e.stopPropagation(); onDuplicate() }}
              className="p-1 text-gray-600 hover:text-blue-600"
              title="Duplicate"
            >
              <Copy className="w-3 h-3" />
            </button>
            <button
              onClick={(e) => { e.stopPropagation(); onToggleLock() }}
              className="p-1 text-gray-600 hover:text-blue-600"
              title={element.locked ? "Unlock" : "Lock"}
            >
              {element.locked ? <Unlock className="w-3 h-3" /> : <Lock className="w-3 h-3" />}
            </button>
            <button
              onClick={(e) => { e.stopPropagation(); onToggleVisibility() }}
              className="p-1 text-gray-600 hover:text-blue-600"
              title="Toggle Visibility"
            >
              {element.visible ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
            </button>
            <button
              onClick={(e) => { e.stopPropagation(); onDelete() }}
              className="p-1 text-gray-600 hover:text-red-600"
              title="Delete"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>
        )}

        {/* Resize Handles */}
        {isSelected && !element.locked && (
          <>
            <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-nw-resize" />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-ne-resize" />
            <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-sw-resize" />
            <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-se-resize" />
          </>
        )}
      </div>
    </div>
  )
}

function renderElementContent(element: CanvasElement): React.ReactNode {
  switch (element.type) {
    case 'text':
      return (
        <div className="w-full h-full flex items-center justify-center bg-gray-50 border border-gray-200 rounded">
          <span className="text-sm text-gray-700">
            {element.properties.content || 'Text Element'}
          </span>
        </div>
      )
    case 'image':
      return (
        <div className="w-full h-full flex items-center justify-center bg-gray-100 border border-gray-200 rounded">
          <ImageIcon className="w-8 h-8 text-gray-400" />
        </div>
      )
    case 'container':
      return (
        <div className="w-full h-full bg-white border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
          <span className="text-xs text-gray-500">Container</span>
        </div>
      )
    case 'infobox':
      return (
        <div className="w-full h-full bg-blue-50 border border-blue-200 rounded p-2">
          <div className="text-xs font-semibold text-blue-800 mb-1">Infobox</div>
          <div className="text-xs text-blue-600">Template content</div>
        </div>
      )
    default:
      return (
        <div className="w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center">
          <span className="text-xs text-gray-500">{element.type}</span>
        </div>
      )
  }
}

function getDefaultProperties(type: string): Record<string, any> {
  switch (type) {
    case 'text':
      return {
        content: 'New Text',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'left'
      }
    case 'image':
      return {
        src: '',
        alt: '',
        objectFit: 'cover'
      }
    case 'container':
      return {
        backgroundColor: '#ffffff',
        padding: 16,
        borderRadius: 4
      }
    case 'infobox':
      return {
        title: 'Infobox Title',
        backgroundColor: '#f8f9fa',
        borderColor: '#a2a9b1'
      }
    default:
      return {}
  }
}

export default VisualCanvas
