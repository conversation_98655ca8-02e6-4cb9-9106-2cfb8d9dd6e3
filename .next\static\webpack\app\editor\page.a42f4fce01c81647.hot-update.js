"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/page",{

/***/ "(app-pages-browser)/./src/components/editor/visual-canvas.tsx":
/*!*************************************************!*\
  !*** ./src/components/editor/visual-canvas.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualCanvas: function() { return /* binding */ VisualCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/unlock.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ VisualCanvas,default auto */ \nvar _s = $RefreshSig$();\n\n\nconst VisualCanvas = (param)=>{\n    let { elements, selectedElementId, onElementsChange, onElementSelect, showGrid, zoom } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resizeHandle, setResizeHandle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tool, setTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"select\");\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.target === canvasRef.current) {\n            onElementSelect(null);\n        }\n    }, [\n        onElementSelect\n    ]);\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.target === canvasRef.current) {\n            const rect = canvasRef.current.getBoundingClientRect();\n            const x = (e.clientX - rect.left) / zoom;\n            const y = (e.clientY - rect.top) / zoom;\n            const newElement = {\n                id: \"text-\".concat(Date.now()),\n                type: \"text\",\n                x: x - 50,\n                y: y - 15,\n                width: 100,\n                height: 30,\n                rotation: 0,\n                locked: false,\n                visible: true,\n                properties: {\n                    content: \"新文本\",\n                    fontSize: 14,\n                    fontWeight: \"normal\",\n                    color: \"#000000\",\n                    textAlign: \"left\"\n                }\n            };\n            onElementsChange([\n                ...elements,\n                newElement\n            ]);\n            onElementSelect(newElement.id);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect,\n        zoom\n    ]);\n    const handleElementClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, elementId)=>{\n        e.stopPropagation();\n        onElementSelect(elementId);\n    }, [\n        onElementSelect\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, elementId)=>{\n        if (tool !== \"select\") return;\n        const element = elements.find((el)=>el.id === elementId);\n        if (!element || element.locked) return;\n        setIsDragging(true);\n        setDragStart({\n            x: e.clientX,\n            y: e.clientY\n        });\n        setDragOffset({\n            x: element.x,\n            y: element.y\n        });\n        onElementSelect(elementId);\n    }, [\n        tool,\n        elements,\n        onElementSelect\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (!isDragging || !selectedElementId) return;\n        const deltaX = e.clientX - dragStart.x;\n        const deltaY = e.clientY - dragStart.y;\n        const updatedElements = elements.map((el)=>{\n            if (el.id === selectedElementId) {\n                return {\n                    ...el,\n                    x: dragOffset.x + deltaX / zoom,\n                    y: dragOffset.y + deltaY / zoom\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        isDragging,\n        selectedElementId,\n        dragStart,\n        dragOffset,\n        elements,\n        onElementsChange,\n        zoom\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(false);\n        setIsResizing(false);\n        setResizeHandle(\"\");\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging || isResizing) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        try {\n            var _canvasRef_current;\n            const data = JSON.parse(e.dataTransfer.getData(\"application/json\"));\n            const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n            if (!rect) return;\n            const x = (e.clientX - rect.left) / zoom;\n            const y = (e.clientY - rect.top) / zoom;\n            const newElement = {\n                id: \"element-\".concat(Date.now()),\n                type: data.type,\n                x,\n                y,\n                width: data.type === \"text\" ? 200 : 150,\n                height: data.type === \"text\" ? 40 : 100,\n                rotation: 0,\n                locked: false,\n                visible: true,\n                properties: getDefaultProperties(data.type)\n            };\n            onElementsChange([\n                ...elements,\n                newElement\n            ]);\n            onElementSelect(newElement.id);\n        } catch (error) {\n            console.error(\"Error handling drop:\", error);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect,\n        zoom\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n    }, []);\n    const duplicateElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const element = elements.find((el)=>el.id === elementId);\n        if (!element) return;\n        const newElement = {\n            ...element,\n            id: \"element-\".concat(Date.now()),\n            x: element.x + 20,\n            y: element.y + 20\n        };\n        onElementsChange([\n            ...elements,\n            newElement\n        ]);\n        onElementSelect(newElement.id);\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect\n    ]);\n    const deleteElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.filter((el)=>el.id !== elementId);\n        onElementsChange(updatedElements);\n        if (selectedElementId === elementId) {\n            onElementSelect(null);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        selectedElementId,\n        onElementSelect\n    ]);\n    const toggleElementLock = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.map((el)=>{\n            if (el.id === elementId) {\n                return {\n                    ...el,\n                    locked: !el.locked\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        elements,\n        onElementsChange\n    ]);\n    const toggleElementVisibility = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.map((el)=>{\n            if (el.id === elementId) {\n                return {\n                    ...el,\n                    visible: !el.visible\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        elements,\n        onElementsChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-lg border border-gray-200 p-2 flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 pr-2 border-r border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setTool(\"select\"),\n                                className: \"p-2 rounded \".concat(tool === \"select\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                                title: \"选择工具 (S)\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setTool(\"text\"),\n                                className: \"p-2 rounded \".concat(tool === \"text\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                                title: \"文本工具 (T) - 双击画布快速添加\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setTool(\"image\"),\n                                className: \"p-2 rounded \".concat(tool === \"image\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                                title: \"图片工具 (I)\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setTool(\"container\"),\n                                className: \"p-2 rounded \".concat(tool === \"container\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                                title: \"容器工具 (C)\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 px-2\",\n                        children: elements.length === 0 ? \"双击画布添加文本\" : \"\".concat(elements.length, \" 个元素\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair bg-white border border-gray-200 rounded-lg shadow-sm\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                onDrop: handleDrop,\n                onDragOver: handleDragOver,\n                style: {\n                    transform: \"scale(\".concat(zoom, \")\"),\n                    transformOrigin: \"top left\",\n                    minHeight: \"600px\"\n                },\n                children: [\n                    showGrid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20 pointer-events-none\",\n                        style: {\n                            backgroundImage: \"\\n                linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n              \",\n                            backgroundSize: \"20px 20px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined),\n                    elements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-gray-400 max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-20 h-20 mx-auto mb-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-4 text-gray-600\",\n                                    children: \"开始创建您的模板\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"从左侧组件面板拖拽元素到画布\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"使用顶部工具栏快速添加元素\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: '点击\"模板库\"按钮选择预制模板'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600\",\n                                        children: \"\\uD83D\\uDCA1 提示：双击画布任意位置快速添加文本元素\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, undefined) : elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanvasElementComponent, {\n                            element: element,\n                            isSelected: selectedElementId === element.id,\n                            onMouseDown: (e)=>handleMouseDown(e, element.id),\n                            onClick: (e)=>handleElementClick(e, element.id),\n                            onDuplicate: ()=>duplicateElement(element.id),\n                            onDelete: ()=>deleteElement(element.id),\n                            onToggleLock: ()=>toggleElementLock(element.id),\n                            onToggleVisibility: ()=>toggleElementVisibility(element.id)\n                        }, element.id, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisualCanvas, \"Va5LGHZOJWUM3HcKbpc9OHBkw3A=\");\n_c = VisualCanvas;\nconst CanvasElementComponent = (param)=>{\n    let { element, isSelected, onMouseDown, onClick, onDuplicate, onDelete, onToggleLock, onToggleVisibility } = param;\n    if (!element.visible) return null;\n    const elementStyle = {\n        position: \"absolute\",\n        left: element.x,\n        top: element.y,\n        width: element.width,\n        height: element.height,\n        transform: \"rotate(\".concat(element.rotation, \"deg)\"),\n        cursor: element.locked ? \"not-allowed\" : \"move\",\n        border: isSelected ? \"2px solid #3b82f6\" : \"1px solid transparent\",\n        borderRadius: \"4px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: elementStyle,\n        onMouseDown: onMouseDown,\n        onClick: onClick,\n        className: \"\\n        \".concat(isSelected ? \"ring-2 ring-blue-500 ring-opacity-50\" : \"\", \"\\n        \").concat(element.locked ? \"opacity-60\" : \"\", \"\\n        hover:border-gray-300 transition-colors\\n      \"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full relative\",\n            children: [\n                renderElementContent(element),\n                isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-8 left-0 flex items-center space-x-1 bg-white rounded shadow-lg border border-gray-200 px-2 py-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onDuplicate();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: \"Duplicate\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onToggleLock();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: element.locked ? \"Unlock\" : \"Lock\",\n                            children: element.locked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 33\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 66\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onToggleVisibility();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: \"Toggle Visibility\",\n                            children: element.visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 34\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 64\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onDelete();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-red-600\",\n                            title: \"Delete\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 11\n                }, undefined),\n                isSelected && !element.locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-nw-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-ne-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-sw-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-se-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n        lineNumber: 373,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = CanvasElementComponent;\nfunction renderElementContent(element) {\n    switch(element.type){\n        case \"text\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex items-center justify-center bg-gray-50 border border-gray-200 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-700\",\n                    children: element.properties.content || \"Text Element\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 439,\n                columnNumber: 9\n            }, this);\n        case \"image\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex items-center justify-center bg-gray-100 border border-gray-200 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-8 h-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this);\n        case \"container\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-white border-2 border-dashed border-gray-300 rounded flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Container\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 453,\n                columnNumber: 9\n            }, this);\n        case \"infobox\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-blue-50 border border-blue-200 rounded p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs font-semibold text-blue-800 mb-1\",\n                        children: \"Infobox\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-600\",\n                        children: \"Template content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 459,\n                columnNumber: 9\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: element.type\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, this);\n    }\n}\nfunction getDefaultProperties(type) {\n    switch(type){\n        case \"text\":\n            return {\n                content: \"New Text\",\n                fontSize: 14,\n                fontWeight: \"normal\",\n                color: \"#000000\",\n                textAlign: \"left\"\n            };\n        case \"image\":\n            return {\n                src: \"\",\n                alt: \"\",\n                objectFit: \"cover\"\n            };\n        case \"container\":\n            return {\n                backgroundColor: \"#ffffff\",\n                padding: 16,\n                borderRadius: 4\n            };\n        case \"infobox\":\n            return {\n                title: \"Infobox Title\",\n                backgroundColor: \"#f8f9fa\",\n                borderColor: \"#a2a9b1\"\n            };\n        default:\n            return {};\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (VisualCanvas);\nvar _c, _c1;\n$RefreshReg$(_c, \"VisualCanvas\");\n$RefreshReg$(_c1, \"CanvasElementComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/editor/visual-canvas.tsx\n"));

/***/ })

});