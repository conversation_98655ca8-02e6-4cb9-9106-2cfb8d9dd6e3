{"name": "mediawiki-template-builder", "version": "0.1.0", "private": true, "description": "A visual tool for creating MediaWiki templates and CSS through drag-and-drop interface", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"powershell -Command Start-Sleep 5; cross-env NODE_ENV=development electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-all": "npm run build && electron-builder --win --mac --linux --publish=never", "dist-portable": "npm run build && electron-builder --win portable --mac zip --linux tar.gz --publish=never", "dist-installer": "npm run build && electron-builder --win nsis --mac dmg --linux AppImage --publish=never", "pack": "electron-builder --dir", "pack-win": "electron-builder --win --dir", "pack-mac": "electron-builder --mac --dir", "pack-linux": "electron-builder --linux --dir", "build-script": "node scripts/build-dist.js", "build-script-help": "node scripts/build-dist.js --help", "test:templates": "node scripts/test-templates.js --all", "test:template": "node scripts/test-templates.js --template", "validate:mediawiki": "node scripts/test-templates.js --all --strict", "test:json": "node scripts/test-templates.js --all --output json"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.2", "@monaco-editor/react": "^4.6.0", "file-saver": "^2.0.5", "framer-motion": "^10.16.16", "jszip": "^3.10.1", "lucide-react": "^0.303.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5", "wait-on": "^7.2.0"}, "main": "electron/main.js", "homepage": "./", "keywords": ["<PERSON><PERSON><PERSON>", "templates", "visual-editor", "wikitext", "css-generator", "drag-and-drop", "desktop-app", "electron"], "author": "MediaWiki Template Builder", "license": "MIT", "build": {"appId": "com.mediawiki.template-builder", "productName": "MediaWiki Template Builder", "directories": {"output": "dist", "buildResources": "build"}, "files": ["out/**/*", "electron/**/*", "node_modules/**/*", "!node_modules/.cache/**/*", "!node_modules/electron/**/*", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}, {"target": "zip", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "publisherName": "MediaWiki Template Builder", "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.developer-tools", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "tar.gz", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Development", "synopsis": "Visual MediaWiki Template Builder", "description": "A desktop application for creating MediaWiki templates with drag-and-drop interface and CSS generation."}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "MediaWiki Template Builder", "uninstallDisplayName": "MediaWiki Template Builder", "license": "LICENSE", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Development"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "dmg": {"title": "${productName} ${version}", "icon": "assets/icon.icns", "iconSize": 100, "contents": [{"x": 380, "y": 280, "type": "link", "path": "/Applications"}, {"x": 110, "y": 280, "type": "file"}], "window": {"width": 540, "height": 380}}, "appImage": {"artifactName": "${productName}-${version}.${ext}"}, "publish": {"provider": "github", "owner": "your-username", "repo": "mediawiki-template-builder"}}}