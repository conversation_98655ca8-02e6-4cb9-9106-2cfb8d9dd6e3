import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { LanguageProvider } from '@/contexts/language-context'
import { ElectronProvider } from '@/components/providers/electron-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'MediaWiki Template Builder',
  description: 'A visual tool for creating beautiful MediaWiki templates and CSS through drag-and-drop interface',
  keywords: ['mediawiki', 'templates', 'visual-editor', 'wikitext', 'css-generator'],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <LanguageProvider>
          <ElectronProvider>
            <div className="min-h-screen bg-gray-50">
              {children}
            </div>
          </ElectronProvider>
        </LanguageProvider>
      </body>
    </html>
  )
}
