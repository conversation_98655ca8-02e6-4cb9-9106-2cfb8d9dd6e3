"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/page",{

/***/ "(app-pages-browser)/./src/app/editor/page.tsx":
/*!*********************************!*\
  !*** ./src/app/editor/page.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tablet.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Grid,HelpCircle,Monitor,Play,Redo,RotateCcw,Save,Smartphone,Tablet,Undo,Upload,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _components_editor_visual_canvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/editor/visual-canvas */ \"(app-pages-browser)/./src/components/editor/visual-canvas.tsx\");\n/* harmony import */ var _components_editor_component_palette__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/editor/component-palette */ \"(app-pages-browser)/./src/components/editor/component-palette.tsx\");\n/* harmony import */ var _components_editor_properties_panel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/editor/properties-panel */ \"(app-pages-browser)/./src/components/editor/properties-panel.tsx\");\n/* harmony import */ var _components_library_template_library__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/library/template-library */ \"(app-pages-browser)/./src/components/library/template-library.tsx\");\n/* harmony import */ var _lib_template_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/template-service */ \"(app-pages-browser)/./src/lib/template-service.ts\");\n/* harmony import */ var _contexts_language_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/language-context */ \"(app-pages-browser)/./src/contexts/language-context.tsx\");\n/* harmony import */ var _components_ui_language_toggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/language-toggle */ \"(app-pages-browser)/./src/components/ui/language-toggle.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_onboarding_guide__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/onboarding-guide */ \"(app-pages-browser)/./src/components/ui/onboarding-guide.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction EditorPage() {\n    _s();\n    const [elements, setElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedElementId, setSelectedElementId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewMode, setPreviewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desktop\");\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isPreviewMode, setIsPreviewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTemplateLibrary, setShowTemplateLibrary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOnboarding, setShowOnboarding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_contexts_language_context__WEBPACK_IMPORTED_MODULE_7__.useLanguage)();\n    // Check if user is new (first time visiting)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hasSeenOnboarding = localStorage.getItem(\"mediawiki-builder-onboarding-seen\");\n        if (!hasSeenOnboarding) {\n            setShowOnboarding(true);\n        }\n    }, []);\n    const handleElementsChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newElements)=>{\n        setElements(newElements);\n    }, []);\n    const handleElementSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        setSelectedElementId(elementId);\n    }, []);\n    const handleElementUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId, updates)=>{\n        setElements((prev)=>prev.map((el)=>el.id === elementId ? {\n                    ...el,\n                    ...updates\n                } : el));\n    }, []);\n    const handleZoomIn = ()=>setZoom((prev)=>Math.min(prev + 0.1, 3));\n    const handleZoomOut = ()=>setZoom((prev)=>Math.max(prev - 0.1, 0.1));\n    const handleZoomReset = ()=>setZoom(1);\n    const selectedElement = selectedElementId ? elements.find((el)=>el.id === selectedElementId) || null : null;\n    const handleTemplateSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((template)=>{\n        const templateService = _lib_template_service__WEBPACK_IMPORTED_MODULE_6__.TemplateService.getInstance();\n        const newElements = templateService.instantiateTemplate(template);\n        setElements((prev)=>[\n                ...prev,\n                ...newElements\n            ]);\n        setShowTemplateLibrary(false);\n    }, []);\n    const handleOnboardingComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        localStorage.setItem(\"mediawiki-builder-onboarding-seen\", \"true\");\n        setShowOnboarding(false);\n    }, []);\n    const handleOnboardingClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        localStorage.setItem(\"mediawiki-builder-onboarding-seen\", \"true\");\n        setShowOnboarding(false);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-2 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: t(\"header.title\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            content: t(\"header.import\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            content: t(\"header.save\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            content: t(\"header.export\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 border-l border-gray-200 pl-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            content: t(\"header.undo\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            content: t(\"header.redo\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                            title: \"Reset View\",\n                                            onClick: handleZoomReset,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 border-l border-gray-200 pl-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleZoomOut,\n                                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                            title: \"Zoom Out\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600 min-w-[3rem] text-center\",\n                                            children: [\n                                                Math.round(zoom * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleZoomIn,\n                                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors\",\n                                            title: \"Zoom In\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowGrid(!showGrid),\n                                    className: \"p-2 rounded transition-colors \".concat(showGrid ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"),\n                                    title: \"Toggle Grid\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPreviewMode(\"desktop\"),\n                                            className: \"p-2 rounded transition-colors \".concat(previewMode === \"desktop\" ? \"bg-white shadow-sm\" : \"hover:bg-gray-200\"),\n                                            title: \"Desktop View\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPreviewMode(\"tablet\"),\n                                            className: \"p-2 rounded transition-colors \".concat(previewMode === \"tablet\" ? \"bg-white shadow-sm\" : \"hover:bg-gray-200\"),\n                                            title: \"Tablet View\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPreviewMode(\"mobile\"),\n                                            className: \"p-2 rounded transition-colors \".concat(previewMode === \"mobile\" ? \"bg-white shadow-sm\" : \"hover:bg-gray-200\"),\n                                            title: \"Mobile View\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsPreviewMode(!isPreviewMode),\n                                    className: \"flex items-center px-4 py-2 rounded-lg font-medium transition-colors \".concat(isPreviewMode ? \"bg-green-600 text-white hover:bg-green-700\" : \"bg-blue-600 text-white hover:bg-blue-700\"),\n                                    children: isPreviewMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Exit Preview\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                    content: t(\"header.help\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowOnboarding(true),\n                                        className: \"flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_language_toggle__WEBPACK_IMPORTED_MODULE_8__.LanguageToggle, {}, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    !isPreviewMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_component_palette__WEBPACK_IMPORTED_MODULE_3__.ComponentPalette, {\n                        className: \"w-64 component-palette\",\n                        onOpenTemplateLibrary: ()=>setShowTemplateLibrary(true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col bg-gray-50 min-h-0\",\n                        children: isPreviewMode ? /* Preview Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n                bg-white shadow-2xl rounded-lg overflow-hidden transition-all duration-300\\n                \".concat(previewMode === \"desktop\" ? \"w-full max-w-6xl\" : \"\", \"\\n                \").concat(previewMode === \"tablet\" ? \"w-full max-w-3xl\" : \"\", \"\\n                \").concat(previewMode === \"mobile\" ? \"w-full max-w-sm\" : \"\", \"\\n              \"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-96 p-8\",\n                                    children: elements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-500 py-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Grid_HelpCircle_Monitor_Play_Redo_RotateCcw_Save_Smartphone_Tablet_Undo_Upload_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-16 w-16 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-medium mb-2\",\n                                                children: \"No Content Yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: \"Exit preview mode to start building your template.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    left: element.x,\n                                                    top: element.y,\n                                                    width: element.width,\n                                                    height: element.height,\n                                                    transform: \"rotate(\".concat(element.rotation, \"deg)\"),\n                                                    display: element.visible ? \"block\" : \"none\"\n                                                },\n                                                children: renderPreviewElement(element)\n                                            }, element.id, false, {\n                                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this) : /* Edit Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_visual_canvas__WEBPACK_IMPORTED_MODULE_2__.VisualCanvas, {\n                            elements: elements,\n                            selectedElementId: selectedElementId,\n                            onElementsChange: handleElementsChange,\n                            onElementSelect: handleElementSelect,\n                            showGrid: showGrid,\n                            zoom: zoom,\n                            className: \"visual-canvas\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    !isPreviewMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_properties_panel__WEBPACK_IMPORTED_MODULE_4__.PropertiesPanel, {\n                        selectedElement: selectedElement,\n                        onElementUpdate: handleElementUpdate,\n                        className: \"w-80 properties-panel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            showTemplateLibrary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_library_template_library__WEBPACK_IMPORTED_MODULE_5__.TemplateLibrary, {\n                onTemplateSelect: handleTemplateSelect,\n                onClose: ()=>setShowTemplateLibrary(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_onboarding_guide__WEBPACK_IMPORTED_MODULE_10__.OnboardingGuide, {\n                isVisible: showOnboarding,\n                onClose: handleOnboardingClose,\n                onComplete: handleOnboardingComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"kS4mGgnu58DGkgFElcZo56syXfs=\", false, function() {\n    return [\n        _contexts_language_context__WEBPACK_IMPORTED_MODULE_7__.useLanguage\n    ];\n});\n_c = EditorPage;\nfunction renderPreviewElement(element) {\n    switch(element.type){\n        case \"text\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex items-center justify-center\",\n                style: {\n                    fontSize: element.properties.fontSize || 14,\n                    color: element.properties.color || \"#000000\",\n                    backgroundColor: element.properties.backgroundColor || \"transparent\"\n                },\n                children: element.properties.content || \"Text Element\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, this);\n        case \"image\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center\",\n                children: element.properties.src ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: element.properties.src,\n                    alt: element.properties.alt || \"\",\n                    className: \"w-full h-full object-cover rounded\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"Image\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 371,\n                columnNumber: 9\n            }, this);\n        case \"container\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full border rounded\",\n                style: {\n                    backgroundColor: element.properties.backgroundColor || \"#ffffff\",\n                    borderRadius: element.properties.borderRadius || 4\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, this);\n        case \"infobox\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-blue-50 border border-blue-200 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-blue-800 mb-2\",\n                        children: element.properties.title || \"Infobox\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-600\",\n                        children: \"Template content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: element.type\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 404,\n                columnNumber: 9\n            }, this);\n    }\n}\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/editor/page.tsx\n"));

/***/ })

});