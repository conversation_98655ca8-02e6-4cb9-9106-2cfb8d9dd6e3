"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/page",{

/***/ "(app-pages-browser)/./src/components/editor/visual-canvas.tsx":
/*!*************************************************!*\
  !*** ./src/components/editor/visual-canvas.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualCanvas: function() { return /* binding */ VisualCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/unlock.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ VisualCanvas,default auto */ \nvar _s = $RefreshSig$();\n\n\nconst VisualCanvas = (param)=>{\n    let { elements, selectedElementId, onElementsChange, onElementSelect, showGrid, zoom } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resizeHandle, setResizeHandle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tool, setTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"select\");\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.target === canvasRef.current) {\n            onElementSelect(null);\n        }\n    }, [\n        onElementSelect\n    ]);\n    const handleCanvasDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.target === canvasRef.current) {\n            const rect = canvasRef.current.getBoundingClientRect();\n            const x = (e.clientX - rect.left) / zoom;\n            const y = (e.clientY - rect.top) / zoom;\n            const newElement = {\n                id: \"text-\".concat(Date.now()),\n                type: \"text\",\n                x: x - 50,\n                y: y - 15,\n                width: 100,\n                height: 30,\n                rotation: 0,\n                locked: false,\n                visible: true,\n                properties: {\n                    content: \"新文本\",\n                    fontSize: 14,\n                    fontWeight: \"normal\",\n                    color: \"#000000\",\n                    textAlign: \"left\"\n                }\n            };\n            onElementsChange([\n                ...elements,\n                newElement\n            ]);\n            onElementSelect(newElement.id);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect,\n        zoom\n    ]);\n    const handleElementClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, elementId)=>{\n        e.stopPropagation();\n        onElementSelect(elementId);\n    }, [\n        onElementSelect\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, elementId)=>{\n        if (tool !== \"select\") return;\n        const element = elements.find((el)=>el.id === elementId);\n        if (!element || element.locked) return;\n        setIsDragging(true);\n        setDragStart({\n            x: e.clientX,\n            y: e.clientY\n        });\n        setDragOffset({\n            x: element.x,\n            y: element.y\n        });\n        onElementSelect(elementId);\n    }, [\n        tool,\n        elements,\n        onElementSelect\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (!isDragging || !selectedElementId) return;\n        const deltaX = e.clientX - dragStart.x;\n        const deltaY = e.clientY - dragStart.y;\n        const updatedElements = elements.map((el)=>{\n            if (el.id === selectedElementId) {\n                return {\n                    ...el,\n                    x: dragOffset.x + deltaX / zoom,\n                    y: dragOffset.y + deltaY / zoom\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        isDragging,\n        selectedElementId,\n        dragStart,\n        dragOffset,\n        elements,\n        onElementsChange,\n        zoom\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(false);\n        setIsResizing(false);\n        setResizeHandle(\"\");\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging || isResizing) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        try {\n            var _canvasRef_current;\n            const data = JSON.parse(e.dataTransfer.getData(\"application/json\"));\n            const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n            if (!rect) return;\n            const x = (e.clientX - rect.left) / zoom;\n            const y = (e.clientY - rect.top) / zoom;\n            const newElement = {\n                id: \"element-\".concat(Date.now()),\n                type: data.type,\n                x,\n                y,\n                width: data.type === \"text\" ? 200 : 150,\n                height: data.type === \"text\" ? 40 : 100,\n                rotation: 0,\n                locked: false,\n                visible: true,\n                properties: getDefaultProperties(data.type)\n            };\n            onElementsChange([\n                ...elements,\n                newElement\n            ]);\n            onElementSelect(newElement.id);\n        } catch (error) {\n            console.error(\"Error handling drop:\", error);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect,\n        zoom\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n    }, []);\n    const duplicateElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const element = elements.find((el)=>el.id === elementId);\n        if (!element) return;\n        const newElement = {\n            ...element,\n            id: \"element-\".concat(Date.now()),\n            x: element.x + 20,\n            y: element.y + 20\n        };\n        onElementsChange([\n            ...elements,\n            newElement\n        ]);\n        onElementSelect(newElement.id);\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect\n    ]);\n    const deleteElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.filter((el)=>el.id !== elementId);\n        onElementsChange(updatedElements);\n        if (selectedElementId === elementId) {\n            onElementSelect(null);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        selectedElementId,\n        onElementSelect\n    ]);\n    const toggleElementLock = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.map((el)=>{\n            if (el.id === elementId) {\n                return {\n                    ...el,\n                    locked: !el.locked\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        elements,\n        onElementsChange\n    ]);\n    const toggleElementVisibility = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.map((el)=>{\n            if (el.id === elementId) {\n                return {\n                    ...el,\n                    visible: !el.visible\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        elements,\n        onElementsChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-lg border border-gray-200 p-2 flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"select\"),\n                        className: \"p-2 rounded \".concat(tool === \"select\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Select Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"text\"),\n                        className: \"p-2 rounded \".concat(tool === \"text\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Text Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"image\"),\n                        className: \"p-2 rounded \".concat(tool === \"image\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Image Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"container\"),\n                        className: \"p-2 rounded \".concat(tool === \"container\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Container Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair bg-white border border-gray-200 rounded-lg shadow-sm\",\n                onClick: handleCanvasClick,\n                onDoubleClick: handleCanvasDoubleClick,\n                onDrop: handleDrop,\n                onDragOver: handleDragOver,\n                style: {\n                    transform: \"scale(\".concat(zoom, \")\"),\n                    transformOrigin: \"top left\",\n                    minHeight: \"600px\"\n                },\n                children: [\n                    showGrid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20 pointer-events-none\",\n                        style: {\n                            backgroundImage: \"\\n                linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n              \",\n                            backgroundSize: \"20px 20px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, undefined),\n                    elements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-gray-400 max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-20 h-20 mx-auto mb-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-4 text-gray-600\",\n                                    children: \"开始创建您的模板\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"从左侧组件面板拖拽元素到画布\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"使用顶部工具栏快速添加元素\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: '点击\"模板库\"按钮选择预制模板'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600\",\n                                        children: \"\\uD83D\\uDCA1 提示：双击画布任意位置快速添加文本元素\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, undefined) : elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanvasElementComponent, {\n                            element: element,\n                            isSelected: selectedElementId === element.id,\n                            onMouseDown: (e)=>handleMouseDown(e, element.id),\n                            onClick: (e)=>handleElementClick(e, element.id),\n                            onDuplicate: ()=>duplicateElement(element.id),\n                            onDelete: ()=>deleteElement(element.id),\n                            onToggleLock: ()=>toggleElementLock(element.id),\n                            onToggleVisibility: ()=>toggleElementVisibility(element.id)\n                        }, element.id, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisualCanvas, \"Va5LGHZOJWUM3HcKbpc9OHBkw3A=\");\n_c = VisualCanvas;\nconst CanvasElementComponent = (param)=>{\n    let { element, isSelected, onMouseDown, onClick, onDuplicate, onDelete, onToggleLock, onToggleVisibility } = param;\n    if (!element.visible) return null;\n    const elementStyle = {\n        position: \"absolute\",\n        left: element.x,\n        top: element.y,\n        width: element.width,\n        height: element.height,\n        transform: \"rotate(\".concat(element.rotation, \"deg)\"),\n        cursor: element.locked ? \"not-allowed\" : \"move\",\n        border: isSelected ? \"2px solid #3b82f6\" : \"1px solid transparent\",\n        borderRadius: \"4px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: elementStyle,\n        onMouseDown: onMouseDown,\n        onClick: onClick,\n        className: \"\\n        \".concat(isSelected ? \"ring-2 ring-blue-500 ring-opacity-50\" : \"\", \"\\n        \").concat(element.locked ? \"opacity-60\" : \"\", \"\\n        hover:border-gray-300 transition-colors\\n      \"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full relative\",\n            children: [\n                renderElementContent(element),\n                isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-8 left-0 flex items-center space-x-1 bg-white rounded shadow-lg border border-gray-200 px-2 py-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onDuplicate();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: \"Duplicate\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onToggleLock();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: element.locked ? \"Unlock\" : \"Lock\",\n                            children: element.locked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 33\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 66\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onToggleVisibility();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: \"Toggle Visibility\",\n                            children: element.visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 34\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 64\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onDelete();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-red-600\",\n                            title: \"Delete\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, undefined),\n                isSelected && !element.locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-nw-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-ne-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-sw-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-se-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = CanvasElementComponent;\nfunction renderElementContent(element) {\n    switch(element.type){\n        case \"text\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex items-center justify-center bg-gray-50 border border-gray-200 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-700\",\n                    children: element.properties.content || \"Text Element\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 433,\n                columnNumber: 9\n            }, this);\n        case \"image\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex items-center justify-center bg-gray-100 border border-gray-200 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-8 h-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 441,\n                columnNumber: 9\n            }, this);\n        case \"container\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-white border-2 border-dashed border-gray-300 rounded flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Container\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this);\n        case \"infobox\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-blue-50 border border-blue-200 rounded p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs font-semibold text-blue-800 mb-1\",\n                        children: \"Infobox\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-600\",\n                        children: \"Template content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 453,\n                columnNumber: 9\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: element.type\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this);\n    }\n}\nfunction getDefaultProperties(type) {\n    switch(type){\n        case \"text\":\n            return {\n                content: \"New Text\",\n                fontSize: 14,\n                fontWeight: \"normal\",\n                color: \"#000000\",\n                textAlign: \"left\"\n            };\n        case \"image\":\n            return {\n                src: \"\",\n                alt: \"\",\n                objectFit: \"cover\"\n            };\n        case \"container\":\n            return {\n                backgroundColor: \"#ffffff\",\n                padding: 16,\n                borderRadius: 4\n            };\n        case \"infobox\":\n            return {\n                title: \"Infobox Title\",\n                backgroundColor: \"#f8f9fa\",\n                borderColor: \"#a2a9b1\"\n            };\n        default:\n            return {};\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (VisualCanvas);\nvar _c, _c1;\n$RefreshReg$(_c, \"VisualCanvas\");\n$RefreshReg$(_c1, \"CanvasElementComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/editor/visual-canvas.tsx\n"));

/***/ })

});