'use client'

import React from 'react'
import { 
  Type, 
  Image as ImageIcon, 
  Square, 
  Table, 
  Info, 
  Navigation, 
  FileText, 
  Layout,
  Grid3X3,
  List,
  Quote,
  Calendar,
  MapPin,
  User,
  Star,
  Tag
} from 'lucide-react'
import { useLanguage } from '@/contexts/language-context'

interface ComponentPaletteProps {
  className?: string
  onOpenTemplateLibrary?: () => void
}

interface PaletteItem {
  id: string
  name: string
  type: string
  icon: React.ReactNode
  category: 'basic' | 'layout' | 'mediawiki' | 'advanced'
  description: string
  preview?: string
}

const paletteItems: PaletteItem[] = [
  // Basic Components
  {
    id: 'text',
    name: 'Text',
    type: 'text',
    icon: <Type className="w-4 h-4" />,
    category: 'basic',
    description: 'Add text content with formatting options',
    preview: 'Sample text content'
  },
  {
    id: 'image',
    name: 'Image',
    type: 'image',
    icon: <ImageIcon className="w-4 h-4" />,
    category: 'basic',
    description: 'Insert images with alt text and captions',
    preview: '[Image placeholder]'
  },
  {
    id: 'container',
    name: 'Container',
    type: 'container',
    icon: <Square className="w-4 h-4" />,
    category: 'basic',
    description: 'Group elements together',
    preview: 'Container box'
  },
  {
    id: 'table',
    name: 'Table',
    type: 'table',
    icon: <Table className="w-4 h-4" />,
    category: 'basic',
    description: 'Create data tables',
    preview: 'Table structure'
  },

  // Layout Components
  {
    id: 'grid',
    name: 'Grid Layout',
    type: 'grid',
    icon: <Grid3X3 className="w-4 h-4" />,
    category: 'layout',
    description: 'Responsive grid system',
    preview: 'Grid layout'
  },
  {
    id: 'columns',
    name: 'Columns',
    type: 'columns',
    icon: <Layout className="w-4 h-4" />,
    category: 'layout',
    description: 'Multi-column layout',
    preview: 'Column layout'
  },
  {
    id: 'list',
    name: 'List',
    type: 'list',
    icon: <List className="w-4 h-4" />,
    category: 'layout',
    description: 'Ordered or unordered lists',
    preview: '• List item'
  },

  // MediaWiki Specific
  {
    id: 'infobox',
    name: 'Infobox',
    type: 'infobox',
    icon: <Info className="w-4 h-4" />,
    category: 'mediawiki',
    description: 'Standard MediaWiki infobox template',
    preview: 'Infobox template'
  },
  {
    id: 'navbox',
    name: 'Navigation Box',
    type: 'navbox',
    icon: <Navigation className="w-4 h-4" />,
    category: 'mediawiki',
    description: 'Navigation template for related articles',
    preview: 'Navigation box'
  },
  {
    id: 'citation',
    name: 'Citation',
    type: 'citation',
    icon: <Quote className="w-4 h-4" />,
    category: 'mediawiki',
    description: 'Reference and citation templates',
    preview: 'Citation format'
  },
  {
    id: 'category',
    name: 'Category Box',
    type: 'category',
    icon: <Tag className="w-4 h-4" />,
    category: 'mediawiki',
    description: 'Category navigation template',
    preview: 'Category box'
  },

  // Advanced Components
  {
    id: 'timeline',
    name: 'Timeline',
    type: 'timeline',
    icon: <Calendar className="w-4 h-4" />,
    category: 'advanced',
    description: 'Interactive timeline component',
    preview: 'Timeline view'
  },
  {
    id: 'location',
    name: 'Location Info',
    type: 'location',
    icon: <MapPin className="w-4 h-4" />,
    category: 'advanced',
    description: 'Geographic information display',
    preview: 'Location data'
  },
  {
    id: 'person',
    name: 'Person Info',
    type: 'person',
    icon: <User className="w-4 h-4" />,
    category: 'advanced',
    description: 'Biographical information template',
    preview: 'Person details'
  },
  {
    id: 'rating',
    name: 'Rating',
    type: 'rating',
    icon: <Star className="w-4 h-4" />,
    category: 'advanced',
    description: 'Star rating or review component',
    preview: '★★★★☆'
  }
]

export const ComponentPalette: React.FC<ComponentPaletteProps> = ({ className = '', onOpenTemplateLibrary }) => {
  const { t } = useLanguage()
  const categories = [
    { id: 'basic', name: 'Basic Elements', color: 'blue' },
    { id: 'layout', name: 'Layout', color: 'green' },
    { id: 'mediawiki', name: 'MediaWiki', color: 'purple' },
    { id: 'advanced', name: 'Advanced', color: 'orange' }
  ]

  const handleDragStart = (e: React.DragEvent, item: PaletteItem) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: item.type,
      name: item.name,
      category: item.category
    }))
    e.dataTransfer.effectAllowed = 'copy'
  }

  return (
    <div className={`bg-white border-r border-gray-200 flex flex-col ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-sm font-semibold text-gray-900 mb-2">{t('palette.title')}</h2>
        <p className="text-xs text-gray-600">{t('palette.subtitle')}</p>
      </div>

      <div className="flex-1 overflow-y-auto">
        {categories.map(category => {
          const categoryItems = paletteItems.filter(item => item.category === category.id)
          
          return (
            <div key={category.id} className="mb-6">
              <div className="px-4 py-2 bg-gray-50 border-b border-gray-100">
                <h3 className="text-xs font-medium text-gray-700 uppercase tracking-wide flex items-center">
                  <div className={`w-2 h-2 rounded-full bg-${category.color}-500 mr-2`} />
                  {category.name}
                </h3>
              </div>
              
              <div className="p-2 space-y-1">
                {categoryItems.map(item => (
                  <PaletteItemComponent
                    key={item.id}
                    item={item}
                    onDragStart={(e) => handleDragStart(e, item)}
                  />
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <h3 className="text-xs font-medium text-gray-700 mb-2">Quick Actions</h3>
        <div className="space-y-2">
          <button className="w-full text-left px-3 py-2 text-xs bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors">
            📋 Import Template
          </button>
          <button className="w-full text-left px-3 py-2 text-xs bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors">
            💾 Save as Component
          </button>
          <button
            onClick={onOpenTemplateLibrary}
            className="w-full text-left px-3 py-2 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            🚀 Browse Library
          </button>
        </div>
      </div>
    </div>
  )
}

interface PaletteItemComponentProps {
  item: PaletteItem
  onDragStart: (e: React.DragEvent) => void
}

const PaletteItemComponent: React.FC<PaletteItemComponentProps> = ({ item, onDragStart }) => {
  return (
    <div
      draggable
      onDragStart={onDragStart}
      className="group flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 cursor-grab active:cursor-grabbing transition-all duration-200 hover:shadow-sm"
      title={item.description}
    >
      <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-colors">
        {item.icon}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-gray-900 group-hover:text-blue-900">
          {item.name}
        </div>
        <div className="text-xs text-gray-500 truncate group-hover:text-blue-700">
          {item.preview}
        </div>
      </div>

      <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="w-1 h-4 bg-blue-400 rounded-full" />
      </div>
    </div>
  )
}

export default ComponentPalette
