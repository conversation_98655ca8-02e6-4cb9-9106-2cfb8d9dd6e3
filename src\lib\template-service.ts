interface CanvasElement {
  id: string
  type: 'text' | 'image' | 'container' | 'table' | 'infobox' | 'navbox' | 'citation'
  x: number
  y: number
  width: number
  height: number
  rotation: number
  locked: boolean
  visible: boolean
  properties: Record<string, any>
  children?: CanvasElement[]
}

interface Template {
  id: string
  name: string
  description: string
  category: 'infobox' | 'navigation' | 'citation' | 'table' | 'layout' | 'specialized'
  tags: string[]
  author: string
  downloads: number
  rating: number
  preview: string
  thumbnail: string
  elements: CanvasElement[]
  createdAt: string
  updatedAt: string
  featured: boolean
  premium: boolean
}

// Pre-built template definitions with actual canvas elements
export const templateDefinitions: Template[] = [
  {
    id: 'infobox-person',
    name: 'Person Infobox',
    description: 'Standard biographical infobox for people, including photo, birth/death dates, occupation, and key details.',
    category: 'infobox',
    tags: ['biography', 'person', 'standard'],
    author: 'MediaWiki Community',
    downloads: 15420,
    rating: 4.8,
    preview: 'Person infobox with photo, dates, and biographical information',
    thumbnail: '/templates/person-infobox.png',
    elements: [
      {
        id: 'person-container',
        type: 'infobox',
        x: 50,
        y: 50,
        width: 300,
        height: 400,
        rotation: 0,
        locked: false,
        visible: true,
        properties: {
          title: 'Person Name',
          backgroundColor: '#f8f9fa',
          borderColor: '#a2a9b1',
          borderWidth: 1,
          borderRadius: 4,
          padding: 16
        },
        children: [
          {
            id: 'person-image',
            type: 'image',
            x: 20,
            y: 20,
            width: 260,
            height: 200,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              src: '',
              alt: 'Person photo',
              objectFit: 'cover',
              borderRadius: 4
            }
          },
          {
            id: 'person-name',
            type: 'text',
            x: 20,
            y: 240,
            width: 260,
            height: 30,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: '{{{name|Person Name}}}',
              fontSize: 18,
              fontWeight: 'bold',
              color: '#000000',
              textAlign: 'center'
            }
          },
          {
            id: 'person-birth',
            type: 'text',
            x: 20,
            y: 280,
            width: 260,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Born: {{{birth_date|}}}',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#333333',
              textAlign: 'left'
            }
          },
          {
            id: 'person-occupation',
            type: 'text',
            x: 20,
            y: 310,
            width: 260,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Occupation: {{{occupation|}}}',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#333333',
              textAlign: 'left'
            }
          }
        ]
      }
    ],
    createdAt: '2024-01-15',
    updatedAt: '2024-06-20',
    featured: true,
    premium: false
  },
  {
    id: 'infobox-location',
    name: 'Location Infobox',
    description: 'Geographic infobox for cities, countries, landmarks with coordinates, population, and area data.',
    category: 'infobox',
    tags: ['geography', 'location', 'coordinates'],
    author: 'GeoWiki Team',
    downloads: 8930,
    rating: 4.6,
    preview: 'Geographic infobox with map, coordinates, and location data',
    thumbnail: '/templates/location-infobox.png',
    elements: [
      {
        id: 'location-container',
        type: 'infobox',
        x: 50,
        y: 50,
        width: 300,
        height: 450,
        rotation: 0,
        locked: false,
        visible: true,
        properties: {
          title: 'Location Name',
          backgroundColor: '#f0f8ff',
          borderColor: '#4682b4',
          borderWidth: 1,
          borderRadius: 4,
          padding: 16
        },
        children: [
          {
            id: 'location-image',
            type: 'image',
            x: 20,
            y: 20,
            width: 260,
            height: 180,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              src: '',
              alt: 'Location photo',
              objectFit: 'cover',
              borderRadius: 4
            }
          },
          {
            id: 'location-name',
            type: 'text',
            x: 20,
            y: 220,
            width: 260,
            height: 30,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: '{{{name|Location Name}}}',
              fontSize: 18,
              fontWeight: 'bold',
              color: '#000000',
              textAlign: 'center'
            }
          },
          {
            id: 'location-coordinates',
            type: 'text',
            x: 20,
            y: 260,
            width: 260,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Coordinates: {{{coordinates|}}}',
              fontSize: 12,
              fontWeight: 'normal',
              color: '#666666',
              textAlign: 'center'
            }
          },
          {
            id: 'location-population',
            type: 'text',
            x: 20,
            y: 290,
            width: 260,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Population: {{{population|}}}',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#333333',
              textAlign: 'left'
            }
          },
          {
            id: 'location-area',
            type: 'text',
            x: 20,
            y: 320,
            width: 260,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Area: {{{area|}}}',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#333333',
              textAlign: 'left'
            }
          }
        ]
      }
    ],
    createdAt: '2024-02-10',
    updatedAt: '2024-06-18',
    featured: true,
    premium: false
  },
  {
    id: 'citation-book',
    name: 'Book Citation',
    description: 'Comprehensive book citation template with author, title, publisher, ISBN, and page references.',
    category: 'citation',
    tags: ['reference', 'book', 'academic'],
    author: 'Academic Wiki',
    downloads: 12340,
    rating: 4.9,
    preview: 'Book citation with full bibliographic details',
    thumbnail: '/templates/book-citation.png',
    elements: [
      {
        id: 'citation-container',
        type: 'container',
        x: 50,
        y: 50,
        width: 500,
        height: 120,
        rotation: 0,
        locked: false,
        visible: true,
        properties: {
          backgroundColor: '#fafafa',
          borderColor: '#e0e0e0',
          borderWidth: 1,
          borderRadius: 4,
          padding: 12
        },
        children: [
          {
            id: 'citation-author',
            type: 'text',
            x: 10,
            y: 10,
            width: 480,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: '{{{author|Author Name}}} ({{{year|Year}}}). ',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#000000',
              textAlign: 'left'
            }
          },
          {
            id: 'citation-title',
            type: 'text',
            x: 10,
            y: 35,
            width: 480,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: '"{{{title|Book Title}}}". ',
              fontSize: 14,
              fontWeight: 'italic',
              color: '#000000',
              textAlign: 'left'
            }
          },
          {
            id: 'citation-publisher',
            type: 'text',
            x: 10,
            y: 60,
            width: 480,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: '{{{publisher|Publisher}}}. ISBN {{{isbn|}}}.',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#000000',
              textAlign: 'left'
            }
          },
          {
            id: 'citation-pages',
            type: 'text',
            x: 10,
            y: 85,
            width: 480,
            height: 20,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'pp. {{{pages|}}}.',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#666666',
              textAlign: 'left'
            }
          }
        ]
      }
    ],
    createdAt: '2024-01-20',
    updatedAt: '2024-06-22',
    featured: true,
    premium: false
  },
  {
    id: 'navbox-series',
    name: 'TV Series Navigation',
    description: 'Navigation box for TV series with seasons, episodes, characters, and related shows.',
    category: 'navigation',
    tags: ['television', 'series', 'episodes'],
    author: 'EntertainmentWiki',
    downloads: 5670,
    rating: 4.7,
    preview: 'TV series navigation with seasons and episodes',
    thumbnail: '/templates/tv-navbox.png',
    elements: [
      {
        id: 'navbox-container',
        type: 'container',
        x: 50,
        y: 50,
        width: 600,
        height: 200,
        rotation: 0,
        locked: false,
        visible: true,
        properties: {
          backgroundColor: '#f8f9fa',
          borderColor: '#a2a9b1',
          borderWidth: 1,
          borderRadius: 4,
          padding: 8
        },
        children: [
          {
            id: 'navbox-title',
            type: 'text',
            x: 10,
            y: 10,
            width: 580,
            height: 30,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: '{{{series_name|TV Series Name}}}',
              fontSize: 16,
              fontWeight: 'bold',
              color: '#000000',
              textAlign: 'center',
              backgroundColor: '#eaecf0'
            }
          },
          {
            id: 'navbox-seasons',
            type: 'text',
            x: 10,
            y: 50,
            width: 580,
            height: 40,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Seasons: {{{seasons|Season 1 • Season 2 • Season 3}}}',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#0645ad',
              textAlign: 'center'
            }
          },
          {
            id: 'navbox-characters',
            type: 'text',
            x: 10,
            y: 100,
            width: 580,
            height: 40,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Main Characters: {{{characters|Character 1 • Character 2 • Character 3}}}',
              fontSize: 14,
              fontWeight: 'normal',
              color: '#0645ad',
              textAlign: 'center'
            }
          },
          {
            id: 'navbox-related',
            type: 'text',
            x: 10,
            y: 150,
            width: 580,
            height: 40,
            rotation: 0,
            locked: false,
            visible: true,
            properties: {
              content: 'Related: {{{related|Related Show 1 • Related Show 2}}}',
              fontSize: 12,
              fontWeight: 'normal',
              color: '#0645ad',
              textAlign: 'center'
            }
          }
        ]
      }
    ],
    createdAt: '2024-03-05',
    updatedAt: '2024-06-15',
    featured: false,
    premium: false
  }
]

export class TemplateService {
  private static instance: TemplateService
  private templates: Template[] = templateDefinitions

  static getInstance(): TemplateService {
    if (!TemplateService.instance) {
      TemplateService.instance = new TemplateService()
    }
    return TemplateService.instance
  }

  getAllTemplates(): Template[] {
    return this.templates
  }

  getTemplateById(id: string): Template | null {
    return this.templates.find(template => template.id === id) || null
  }

  getTemplatesByCategory(category: string): Template[] {
    if (category === 'all') return this.templates
    return this.templates.filter(template => template.category === category)
  }

  getFeaturedTemplates(): Template[] {
    return this.templates.filter(template => template.featured)
  }

  searchTemplates(query: string): Template[] {
    const lowercaseQuery = query.toLowerCase()
    return this.templates.filter(template =>
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    )
  }

  // Convert template elements to canvas elements with new IDs
  instantiateTemplate(template: Template): CanvasElement[] {
    const generateNewId = () => `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    const cloneElement = (element: CanvasElement): CanvasElement => {
      const cloned: CanvasElement = {
        ...element,
        id: generateNewId(),
        children: element.children?.map(child => cloneElement(child))
      }
      return cloned
    }

    return template.elements.map(element => cloneElement(element))
  }

  // Generate MediaWiki code from template
  generateWikitext(template: Template): string {
    switch (template.category) {
      case 'infobox':
        return this.generateInfoboxWikitext(template)
      case 'citation':
        return this.generateCitationWikitext(template)
      case 'navigation':
        return this.generateNavboxWikitext(template)
      default:
        return `<!-- ${template.name} template -->\n{{${template.name}\n| parameter1 = value1\n| parameter2 = value2\n}}`
    }
  }

  private generateInfoboxWikitext(template: Template): string {
    const templateName = template.name.replace(/\s+/g, '_').toLowerCase()
    return `{{Infobox ${templateName}
| name = {{{name|}}}
| image = {{{image|}}}
| caption = {{{caption|}}}
| birth_date = {{{birth_date|}}}
| birth_place = {{{birth_place|}}}
| occupation = {{{occupation|}}}
| known_for = {{{known_for|}}}
}}`
  }

  private generateCitationWikitext(template: Template): string {
    return `{{cite book
| author = {{{author|}}}
| title = {{{title|}}}
| publisher = {{{publisher|}}}
| year = {{{year|}}}
| isbn = {{{isbn|}}}
| pages = {{{pages|}}}
}}`
  }

  private generateNavboxWikitext(template: Template): string {
    return `{{Navbox
| name = {{{name|}}}
| title = {{{title|}}}
| group1 = Seasons
| list1 = {{{seasons|}}}
| group2 = Characters
| list2 = {{{characters|}}}
| group3 = Related
| list3 = {{{related|}}}
}}`
  }
}

export default TemplateService
