'use client'

import { useState, useCallback } from 'react'
import {
  FileText,
  Palette,
  Eye,
  Download,
  Upload,
  Save,
  Undo,
  Redo,
  Grid,
  Smartphone,
  Tablet,
  Monitor,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Play,
  HelpCircle
} from 'lucide-react'
import { VisualCanvas } from '@/components/editor/visual-canvas'
import { ComponentPalette } from '@/components/editor/component-palette'
import { PropertiesPanel } from '@/components/editor/properties-panel'
import { TemplateLibrary } from '@/components/library/template-library'
import { TemplateService } from '@/lib/template-service'
import { useLanguage } from '@/contexts/language-context'
import { LanguageToggle } from '@/components/ui/language-toggle'
import { Tooltip } from '@/components/ui/tooltip'
import { OnboardingGuide } from '@/components/ui/onboarding-guide'

interface CanvasElement {
  id: string
  type: 'text' | 'image' | 'container' | 'table' | 'infobox'
  x: number
  y: number
  width: number
  height: number
  rotation: number
  locked: boolean
  visible: boolean
  properties: Record<string, any>
  children?: CanvasElement[]
}

export default function EditorPage() {
  const [elements, setElements] = useState<CanvasElement[]>([])
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null)
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [showGrid, setShowGrid] = useState(true)
  const [zoom, setZoom] = useState(1)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [showTemplateLibrary, setShowTemplateLibrary] = useState(false)
  const [showOnboarding, setShowOnboarding] = useState(false)
  const { t } = useLanguage()

  // Check if user is new (first time visiting)
  useEffect(() => {
    const hasSeenOnboarding = localStorage.getItem('mediawiki-builder-onboarding-seen')
    if (!hasSeenOnboarding) {
      setShowOnboarding(true)
    }
  }, [])

  const handleElementsChange = useCallback((newElements: CanvasElement[]) => {
    setElements(newElements)
  }, [])

  const handleElementSelect = useCallback((elementId: string | null) => {
    setSelectedElementId(elementId)
  }, [])

  const handleElementUpdate = useCallback((elementId: string, updates: Partial<CanvasElement>) => {
    setElements(prev => prev.map(el =>
      el.id === elementId ? { ...el, ...updates } : el
    ))
  }, [])

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 3))
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.1))
  const handleZoomReset = () => setZoom(1)

  const selectedElement = selectedElementId ? elements.find(el => el.id === selectedElementId) || null : null

  const handleTemplateSelect = useCallback((template: any) => {
    const templateService = TemplateService.getInstance()
    const newElements = templateService.instantiateTemplate(template)
    setElements(prev => [...prev, ...newElements])
    setShowTemplateLibrary(false)
  }, [])

  const handleOnboardingComplete = useCallback(() => {
    localStorage.setItem('mediawiki-builder-onboarding-seen', 'true')
    setShowOnboarding(false)
  }, [])

  const handleOnboardingClose = useCallback(() => {
    localStorage.setItem('mediawiki-builder-onboarding-seen', 'true')
    setShowOnboarding(false)
  }, [])

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header Toolbar */}
      <header className="bg-white border-b border-gray-200 px-4 py-2 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <FileText className="h-6 w-6 text-blue-600 mr-2" />
              <h1 className="text-lg font-semibold text-gray-900">{t('header.title')}</h1>
            </div>

            {/* File Operations */}
            <div className="flex items-center space-x-1">
              <Tooltip content={t('header.import')}>
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors">
                  <Upload className="h-4 w-4" />
                </button>
              </Tooltip>
              <Tooltip content={t('header.save')}>
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors">
                  <Save className="h-4 w-4" />
                </button>
              </Tooltip>
              <Tooltip content={t('header.export')}>
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors">
                  <Download className="h-4 w-4" />
                </button>
              </Tooltip>
            </div>

            {/* Edit Operations */}
            <div className="flex items-center space-x-1 border-l border-gray-200 pl-4">
              <Tooltip content={t('header.undo')}>
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors">
                  <Undo className="h-4 w-4" />
                </button>
              </Tooltip>
              <Tooltip content={t('header.redo')}>
                <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors">
                  <Redo className="h-4 w-4" />
                </button>
              </Tooltip>
              <button
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                title="Reset View"
                onClick={handleZoomReset}
              >
                <RotateCcw className="h-4 w-4" />
              </button>
            </div>

            {/* Zoom Controls */}
            <div className="flex items-center space-x-1 border-l border-gray-200 pl-4">
              <button
                onClick={handleZoomOut}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                title="Zoom Out"
              >
                <ZoomOut className="h-4 w-4" />
              </button>
              <span className="text-xs text-gray-600 min-w-[3rem] text-center">
                {Math.round(zoom * 100)}%
              </span>
              <button
                onClick={handleZoomIn}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                title="Zoom In"
              >
                <ZoomIn className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Right Controls */}
          <div className="flex items-center space-x-4">
            {/* Grid Toggle */}
            <button
              onClick={() => setShowGrid(!showGrid)}
              className={`p-2 rounded transition-colors ${
                showGrid
                  ? 'bg-blue-100 text-blue-600'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
              title="Toggle Grid"
            >
              <Grid className="h-4 w-4" />
            </button>

            {/* Device Preview */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setPreviewMode('desktop')}
                className={`p-2 rounded transition-colors ${
                  previewMode === 'desktop' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                }`}
                title="Desktop View"
              >
                <Monitor className="h-4 w-4" />
              </button>
              <button
                onClick={() => setPreviewMode('tablet')}
                className={`p-2 rounded transition-colors ${
                  previewMode === 'tablet' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                }`}
                title="Tablet View"
              >
                <Tablet className="h-4 w-4" />
              </button>
              <button
                onClick={() => setPreviewMode('mobile')}
                className={`p-2 rounded transition-colors ${
                  previewMode === 'mobile' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                }`}
                title="Mobile View"
              >
                <Smartphone className="h-4 w-4" />
              </button>
            </div>

            {/* Preview Mode Toggle */}
            <button
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                isPreviewMode
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isPreviewMode ? (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Exit Preview
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Preview
                </>
              )}
            </button>

            {/* Help Button */}
            <Tooltip content={t('header.help')}>
              <button
                onClick={() => setShowOnboarding(true)}
                className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <HelpCircle className="h-4 w-4" />
              </button>
            </Tooltip>

            {/* Language Toggle */}
            <LanguageToggle />
          </div>
        </div>
      </header>

      {/* Main Editor Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Component Palette */}
        {!isPreviewMode && (
          <ComponentPalette
            className="w-64 component-palette"
            onOpenTemplateLibrary={() => setShowTemplateLibrary(true)}
          />
        )}

        {/* Center - Canvas Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          {isPreviewMode ? (
            /* Preview Mode */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className={`
                bg-white shadow-2xl rounded-lg overflow-hidden transition-all duration-300
                ${previewMode === 'desktop' ? 'w-full max-w-6xl' : ''}
                ${previewMode === 'tablet' ? 'w-full max-w-3xl' : ''}
                ${previewMode === 'mobile' ? 'w-full max-w-sm' : ''}
              `}>
                <div className="min-h-96 p-8">
                  {elements.length === 0 ? (
                    <div className="text-center text-gray-500 py-16">
                      <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                      <h3 className="text-xl font-medium mb-2">No Content Yet</h3>
                      <p className="text-sm">Exit preview mode to start building your template.</p>
                    </div>
                  ) : (
                    <div className="relative">
                      {/* Render elements in preview mode */}
                      {elements.map(element => (
                        <div
                          key={element.id}
                          style={{
                            position: 'absolute',
                            left: element.x,
                            top: element.y,
                            width: element.width,
                            height: element.height,
                            transform: `rotate(${element.rotation}deg)`,
                            display: element.visible ? 'block' : 'none'
                          }}
                        >
                          {renderPreviewElement(element)}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            /* Edit Mode */
            <VisualCanvas
              elements={elements}
              selectedElementId={selectedElementId}
              onElementsChange={handleElementsChange}
              onElementSelect={handleElementSelect}
              showGrid={showGrid}
              zoom={zoom}
              className="visual-canvas"
            />
          )}
        </div>

        {/* Right Sidebar - Properties Panel */}
        {!isPreviewMode && (
          <PropertiesPanel
            selectedElement={selectedElement}
            onElementUpdate={handleElementUpdate}
            className="w-80 properties-panel"
          />
        )}
      </div>

      {/* Template Library Modal */}
      {showTemplateLibrary && (
        <TemplateLibrary
          onTemplateSelect={handleTemplateSelect}
          onClose={() => setShowTemplateLibrary(false)}
        />
      )}

      {/* Onboarding Guide */}
      <OnboardingGuide
        isVisible={showOnboarding}
        onClose={handleOnboardingClose}
        onComplete={handleOnboardingComplete}
      />
    </div>
  )
}

function renderPreviewElement(element: any): React.ReactNode {
  switch (element.type) {
    case 'text':
      return (
        <div
          className="w-full h-full flex items-center justify-center"
          style={{
            fontSize: element.properties.fontSize || 14,
            color: element.properties.color || '#000000',
            backgroundColor: element.properties.backgroundColor || 'transparent'
          }}
        >
          {element.properties.content || 'Text Element'}
        </div>
      )
    case 'image':
      return (
        <div className="w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center">
          {element.properties.src ? (
            <img
              src={element.properties.src}
              alt={element.properties.alt || ''}
              className="w-full h-full object-cover rounded"
            />
          ) : (
            <span className="text-gray-500 text-sm">Image</span>
          )}
        </div>
      )
    case 'container':
      return (
        <div
          className="w-full h-full border rounded"
          style={{
            backgroundColor: element.properties.backgroundColor || '#ffffff',
            borderRadius: element.properties.borderRadius || 4
          }}
        />
      )
    case 'infobox':
      return (
        <div className="w-full h-full bg-blue-50 border border-blue-200 rounded p-4">
          <div className="font-semibold text-blue-800 mb-2">
            {element.properties.title || 'Infobox'}
          </div>
          <div className="text-sm text-blue-600">Template content</div>
        </div>
      )
    default:
      return (
        <div className="w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center">
          <span className="text-gray-500 text-sm">{element.type}</span>
        </div>
      )
  }
}


