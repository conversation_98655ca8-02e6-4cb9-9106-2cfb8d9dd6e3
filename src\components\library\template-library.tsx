'use client'

import React, { useState, useEffect } from 'react'
import { 
  Search, 
  Filter, 
  Star, 
  Download, 
  Eye, 
  Copy, 
  Tag,
  Calendar,
  User,
  FileText,
  Grid3X3,
  Navigation,
  Info,
  Quote,
  MapPin,
  Sparkles,
  Heart,
  Bookmark,
  Clock
} from 'lucide-react'

interface Template {
  id: string
  name: string
  description: string
  category: 'infobox' | 'navigation' | 'citation' | 'table' | 'layout' | 'specialized'
  tags: string[]
  author: string
  downloads: number
  rating: number
  preview: string
  thumbnail: string
  elements: any[]
  createdAt: string
  updatedAt: string
  featured: boolean
  premium: boolean
}

interface TemplateLibraryProps {
  onTemplateSelect: (template: Template) => void
  onClose: () => void
}

const sampleTemplates: Template[] = [
  {
    id: 'infobox-person',
    name: 'Person Infobox',
    description: 'Standard biographical infobox for people, including photo, birth/death dates, occupation, and key details.',
    category: 'infobox',
    tags: ['biography', 'person', 'standard'],
    author: 'MediaWiki Community',
    downloads: 15420,
    rating: 4.8,
    preview: 'Person infobox with photo, dates, and biographical information',
    thumbnail: '/templates/person-infobox.png',
    elements: [],
    createdAt: '2024-01-15',
    updatedAt: '2024-06-20',
    featured: true,
    premium: false
  },
  {
    id: 'infobox-location',
    name: 'Location Infobox',
    description: 'Geographic infobox for cities, countries, landmarks with coordinates, population, and area data.',
    category: 'infobox',
    tags: ['geography', 'location', 'coordinates'],
    author: 'GeoWiki Team',
    downloads: 8930,
    rating: 4.6,
    preview: 'Geographic infobox with map, coordinates, and location data',
    thumbnail: '/templates/location-infobox.png',
    elements: [],
    createdAt: '2024-02-10',
    updatedAt: '2024-06-18',
    featured: true,
    premium: false
  },
  {
    id: 'navbox-series',
    name: 'TV Series Navigation',
    description: 'Navigation box for TV series with seasons, episodes, characters, and related shows.',
    category: 'navigation',
    tags: ['television', 'series', 'episodes'],
    author: 'EntertainmentWiki',
    downloads: 5670,
    rating: 4.7,
    preview: 'TV series navigation with seasons and episodes',
    thumbnail: '/templates/tv-navbox.png',
    elements: [],
    createdAt: '2024-03-05',
    updatedAt: '2024-06-15',
    featured: false,
    premium: false
  },
  {
    id: 'citation-book',
    name: 'Book Citation',
    description: 'Comprehensive book citation template with author, title, publisher, ISBN, and page references.',
    category: 'citation',
    tags: ['reference', 'book', 'academic'],
    author: 'Academic Wiki',
    downloads: 12340,
    rating: 4.9,
    preview: 'Book citation with full bibliographic details',
    thumbnail: '/templates/book-citation.png',
    elements: [],
    createdAt: '2024-01-20',
    updatedAt: '2024-06-22',
    featured: true,
    premium: false
  },
  {
    id: 'table-comparison',
    name: 'Comparison Table',
    description: 'Multi-column comparison table with highlighting, sorting, and responsive design.',
    category: 'table',
    tags: ['comparison', 'data', 'responsive'],
    author: 'DataWiki Pro',
    downloads: 3450,
    rating: 4.5,
    preview: 'Comparison table with multiple columns and highlighting',
    thumbnail: '/templates/comparison-table.png',
    elements: [],
    createdAt: '2024-04-12',
    updatedAt: '2024-06-10',
    featured: false,
    premium: true
  },
  {
    id: 'layout-timeline',
    name: 'Timeline Layout',
    description: 'Interactive timeline layout for historical events, project milestones, or chronological data.',
    category: 'layout',
    tags: ['timeline', 'history', 'chronology'],
    author: 'HistoryWiki',
    downloads: 2890,
    rating: 4.4,
    preview: 'Timeline layout with events and dates',
    thumbnail: '/templates/timeline-layout.png',
    elements: [],
    createdAt: '2024-05-01',
    updatedAt: '2024-06-08',
    featured: false,
    premium: false
  }
]

export const TemplateLibrary: React.FC<TemplateLibraryProps> = ({ onTemplateSelect, onClose }) => {
  const [templates, setTemplates] = useState<Template[]>(sampleTemplates)
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>(sampleTemplates)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'popular' | 'recent' | 'rating'>('popular')
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false)

  const categories = [
    { id: 'all', name: 'All Templates', icon: Grid3X3, count: templates.length },
    { id: 'infobox', name: 'Infoboxes', icon: Info, count: templates.filter(t => t.category === 'infobox').length },
    { id: 'navigation', name: 'Navigation', icon: Navigation, count: templates.filter(t => t.category === 'navigation').length },
    { id: 'citation', name: 'Citations', icon: Quote, count: templates.filter(t => t.category === 'citation').length },
    { id: 'table', name: 'Tables', icon: Grid3X3, count: templates.filter(t => t.category === 'table').length },
    { id: 'layout', name: 'Layouts', icon: FileText, count: templates.filter(t => t.category === 'layout').length },
    { id: 'specialized', name: 'Specialized', icon: Sparkles, count: templates.filter(t => t.category === 'specialized').length }
  ]

  useEffect(() => {
    let filtered = templates

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory)
    }

    // Filter by featured
    if (showFeaturedOnly) {
      filtered = filtered.filter(template => template.featured)
    }

    // Sort templates
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.downloads - a.downloads
        case 'recent':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        case 'rating':
          return b.rating - a.rating
        default:
          return 0
      }
    })

    setFilteredTemplates(filtered)
  }, [templates, searchQuery, selectedCategory, sortBy, showFeaturedOnly])

  const handleTemplateUse = (template: Template) => {
    onTemplateSelect(template)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <Bookmark className="w-6 h-6 mr-2 text-blue-600" />
                Template Library
              </h2>
              <p className="text-gray-600 mt-1">Choose from professionally designed MediaWiki templates</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          {/* Search and Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="popular">Most Popular</option>
              <option value="recent">Recently Updated</option>
              <option value="rating">Highest Rated</option>
            </select>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showFeaturedOnly}
                onChange={(e) => setShowFeaturedOnly(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
              />
              <span className="text-sm text-gray-700">Featured only</span>
            </label>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Categories Sidebar */}
          <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Categories</h3>
            <div className="space-y-1">
              {categories.map(category => {
                const Icon = category.icon
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center">
                      <Icon className="w-4 h-4 mr-2" />
                      {category.name}
                    </div>
                    <span className="text-xs text-gray-500">{category.count}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Templates Grid */}
          <div className="flex-1 p-6 overflow-y-auto">
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-16">
                <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                <p className="text-gray-600">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTemplates.map(template => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onUse={() => handleTemplateUse(template)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

interface TemplateCardProps {
  template: Template
  onUse: () => void
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onUse }) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden">
      {/* Template Preview */}
      <div className="h-32 bg-gray-100 flex items-center justify-center relative">
        {template.featured && (
          <div className="absolute top-2 left-2 bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium flex items-center">
            <Star className="w-3 h-3 mr-1" />
            Featured
          </div>
        )}
        {template.premium && (
          <div className="absolute top-2 right-2 bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
            <Sparkles className="w-3 h-3 mr-1" />
            Pro
          </div>
        )}
        <div className="text-center text-gray-500">
          <FileText className="w-8 h-8 mx-auto mb-2" />
          <span className="text-xs">Preview</span>
        </div>
      </div>

      {/* Template Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 text-sm">{template.name}</h3>
          <div className="flex items-center text-xs text-gray-500">
            <Star className="w-3 h-3 mr-1 text-yellow-400" />
            {template.rating}
          </div>
        </div>
        
        <p className="text-xs text-gray-600 mb-3 line-clamp-2">{template.description}</p>
        
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <span className="flex items-center">
            <Download className="w-3 h-3 mr-1" />
            {template.downloads.toLocaleString()}
          </span>
          <span className="flex items-center">
            <User className="w-3 h-3 mr-1" />
            {template.author}
          </span>
        </div>

        <div className="flex flex-wrap gap-1 mb-3">
          {template.tags.slice(0, 3).map(tag => (
            <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
              {tag}
            </span>
          ))}
        </div>

        <div className="flex space-x-2">
          <button
            onClick={onUse}
            className="flex-1 bg-blue-600 text-white text-xs py-2 px-3 rounded hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <Copy className="w-3 h-3 mr-1" />
            Use Template
          </button>
          <button className="px-3 py-2 border border-gray-300 text-gray-600 text-xs rounded hover:bg-gray-50 transition-colors">
            <Eye className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default TemplateLibrary
