"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/page",{

/***/ "(app-pages-browser)/./src/components/editor/visual-canvas.tsx":
/*!*************************************************!*\
  !*** ./src/components/editor/visual-canvas.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualCanvas: function() { return /* binding */ VisualCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/unlock.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Eye,EyeOff,Image,Layers,Lock,MousePointer,Square,Trash2,Type,Unlock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ VisualCanvas,default auto */ \nvar _s = $RefreshSig$();\n\n\nconst VisualCanvas = (param)=>{\n    let { elements, selectedElementId, onElementsChange, onElementSelect, showGrid, zoom } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resizeHandle, setResizeHandle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tool, setTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"select\");\n    const handleCanvasClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.target === canvasRef.current) {\n            onElementSelect(null);\n        }\n    }, [\n        onElementSelect\n    ]);\n    const handleElementClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, elementId)=>{\n        e.stopPropagation();\n        onElementSelect(elementId);\n    }, [\n        onElementSelect\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e, elementId)=>{\n        if (tool !== \"select\") return;\n        const element = elements.find((el)=>el.id === elementId);\n        if (!element || element.locked) return;\n        setIsDragging(true);\n        setDragStart({\n            x: e.clientX,\n            y: e.clientY\n        });\n        setDragOffset({\n            x: element.x,\n            y: element.y\n        });\n        onElementSelect(elementId);\n    }, [\n        tool,\n        elements,\n        onElementSelect\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (!isDragging || !selectedElementId) return;\n        const deltaX = e.clientX - dragStart.x;\n        const deltaY = e.clientY - dragStart.y;\n        const updatedElements = elements.map((el)=>{\n            if (el.id === selectedElementId) {\n                return {\n                    ...el,\n                    x: dragOffset.x + deltaX / zoom,\n                    y: dragOffset.y + deltaY / zoom\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        isDragging,\n        selectedElementId,\n        dragStart,\n        dragOffset,\n        elements,\n        onElementsChange,\n        zoom\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(false);\n        setIsResizing(false);\n        setResizeHandle(\"\");\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging || isResizing) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        try {\n            var _canvasRef_current;\n            const data = JSON.parse(e.dataTransfer.getData(\"application/json\"));\n            const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n            if (!rect) return;\n            const x = (e.clientX - rect.left) / zoom;\n            const y = (e.clientY - rect.top) / zoom;\n            const newElement = {\n                id: \"element-\".concat(Date.now()),\n                type: data.type,\n                x,\n                y,\n                width: data.type === \"text\" ? 200 : 150,\n                height: data.type === \"text\" ? 40 : 100,\n                rotation: 0,\n                locked: false,\n                visible: true,\n                properties: getDefaultProperties(data.type)\n            };\n            onElementsChange([\n                ...elements,\n                newElement\n            ]);\n            onElementSelect(newElement.id);\n        } catch (error) {\n            console.error(\"Error handling drop:\", error);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect,\n        zoom\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n    }, []);\n    const duplicateElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const element = elements.find((el)=>el.id === elementId);\n        if (!element) return;\n        const newElement = {\n            ...element,\n            id: \"element-\".concat(Date.now()),\n            x: element.x + 20,\n            y: element.y + 20\n        };\n        onElementsChange([\n            ...elements,\n            newElement\n        ]);\n        onElementSelect(newElement.id);\n    }, [\n        elements,\n        onElementsChange,\n        onElementSelect\n    ]);\n    const deleteElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.filter((el)=>el.id !== elementId);\n        onElementsChange(updatedElements);\n        if (selectedElementId === elementId) {\n            onElementSelect(null);\n        }\n    }, [\n        elements,\n        onElementsChange,\n        selectedElementId,\n        onElementSelect\n    ]);\n    const toggleElementLock = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.map((el)=>{\n            if (el.id === elementId) {\n                return {\n                    ...el,\n                    locked: !el.locked\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        elements,\n        onElementsChange\n    ]);\n    const toggleElementVisibility = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((elementId)=>{\n        const updatedElements = elements.map((el)=>{\n            if (el.id === elementId) {\n                return {\n                    ...el,\n                    visible: !el.visible\n                };\n            }\n            return el;\n        });\n        onElementsChange(updatedElements);\n    }, [\n        elements,\n        onElementsChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10 bg-white rounded-lg shadow-lg border border-gray-200 p-2 flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"select\"),\n                        className: \"p-2 rounded \".concat(tool === \"select\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Select Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"text\"),\n                        className: \"p-2 rounded \".concat(tool === \"text\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Text Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"image\"),\n                        className: \"p-2 rounded \".concat(tool === \"image\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Image Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setTool(\"container\"),\n                        className: \"p-2 rounded \".concat(tool === \"container\" ? \"bg-blue-100 text-blue-600\" : \"text-gray-600 hover:bg-gray-100\"),\n                        title: \"Container Tool\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: canvasRef,\n                className: \"w-full h-full relative cursor-crosshair bg-white border border-gray-200 rounded-lg shadow-sm\",\n                onClick: handleCanvasClick,\n                onDrop: handleDrop,\n                onDragOver: handleDragOver,\n                style: {\n                    transform: \"scale(\".concat(zoom, \")\"),\n                    transformOrigin: \"top left\",\n                    minHeight: \"600px\"\n                },\n                children: [\n                    showGrid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20 pointer-events-none\",\n                        style: {\n                            backgroundImage: \"\\n                linear-gradient(to right, #e5e7eb 1px, transparent 1px),\\n                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\\n              \",\n                            backgroundSize: \"20px 20px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, undefined),\n                    elements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-gray-400 max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-20 h-20 mx-auto mb-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-4 text-gray-600\",\n                                    children: \"开始创建您的模板\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"从左侧组件面板拖拽元素到画布\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"使用顶部工具栏快速添加元素\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: '点击\"模板库\"按钮选择预制模板'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600\",\n                                        children: \"\\uD83D\\uDCA1 提示：双击画布任意位置快速添加文本元素\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined) : elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanvasElementComponent, {\n                            element: element,\n                            isSelected: selectedElementId === element.id,\n                            onMouseDown: (e)=>handleMouseDown(e, element.id),\n                            onClick: (e)=>handleElementClick(e, element.id),\n                            onDuplicate: ()=>duplicateElement(element.id),\n                            onDelete: ()=>deleteElement(element.id),\n                            onToggleLock: ()=>toggleElementLock(element.id),\n                            onToggleVisibility: ()=>toggleElementVisibility(element.id)\n                        }, element.id, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisualCanvas, \"wsmaijyiSte1UWebMmAILoUjEpo=\");\n_c = VisualCanvas;\nconst CanvasElementComponent = (param)=>{\n    let { element, isSelected, onMouseDown, onClick, onDuplicate, onDelete, onToggleLock, onToggleVisibility } = param;\n    if (!element.visible) return null;\n    const elementStyle = {\n        position: \"absolute\",\n        left: element.x,\n        top: element.y,\n        width: element.width,\n        height: element.height,\n        transform: \"rotate(\".concat(element.rotation, \"deg)\"),\n        cursor: element.locked ? \"not-allowed\" : \"move\",\n        border: isSelected ? \"2px solid #3b82f6\" : \"1px solid transparent\",\n        borderRadius: \"4px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: elementStyle,\n        onMouseDown: onMouseDown,\n        onClick: onClick,\n        className: \"\\n        \".concat(isSelected ? \"ring-2 ring-blue-500 ring-opacity-50\" : \"\", \"\\n        \").concat(element.locked ? \"opacity-60\" : \"\", \"\\n        hover:border-gray-300 transition-colors\\n      \"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full relative\",\n            children: [\n                renderElementContent(element),\n                isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-8 left-0 flex items-center space-x-1 bg-white rounded shadow-lg border border-gray-200 px-2 py-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onDuplicate();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: \"Duplicate\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onToggleLock();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: element.locked ? \"Unlock\" : \"Lock\",\n                            children: element.locked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 33\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 66\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onToggleVisibility();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-blue-600\",\n                            title: \"Toggle Visibility\",\n                            children: element.visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 34\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 64\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                onDelete();\n                            },\n                            className: \"p-1 text-gray-600 hover:text-red-600\",\n                            title: \"Delete\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 11\n                }, undefined),\n                isSelected && !element.locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-nw-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-ne-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full cursor-sw-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full cursor-se-resize\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n            lineNumber: 347,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = CanvasElementComponent;\nfunction renderElementContent(element) {\n    switch(element.type){\n        case \"text\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex items-center justify-center bg-gray-50 border border-gray-200 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-700\",\n                    children: element.properties.content || \"Text Element\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, this);\n        case \"image\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex items-center justify-center bg-gray-100 border border-gray-200 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Eye_EyeOff_Image_Layers_Lock_MousePointer_Square_Trash2_Type_Unlock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-8 h-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 410,\n                columnNumber: 9\n            }, this);\n        case \"container\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-white border-2 border-dashed border-gray-300 rounded flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Container\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 416,\n                columnNumber: 9\n            }, this);\n        case \"infobox\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-blue-50 border border-blue-200 rounded p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs font-semibold text-blue-800 mb-1\",\n                        children: \"Infobox\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-600\",\n                        children: \"Template content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 422,\n                columnNumber: 9\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-gray-100 border border-gray-200 rounded flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: element.type\n                }, void 0, false, {\n                    fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Mediawiki\\\\src\\\\components\\\\editor\\\\visual-canvas.tsx\",\n                lineNumber: 429,\n                columnNumber: 9\n            }, this);\n    }\n}\nfunction getDefaultProperties(type) {\n    switch(type){\n        case \"text\":\n            return {\n                content: \"New Text\",\n                fontSize: 14,\n                fontWeight: \"normal\",\n                color: \"#000000\",\n                textAlign: \"left\"\n            };\n        case \"image\":\n            return {\n                src: \"\",\n                alt: \"\",\n                objectFit: \"cover\"\n            };\n        case \"container\":\n            return {\n                backgroundColor: \"#ffffff\",\n                padding: 16,\n                borderRadius: 4\n            };\n        case \"infobox\":\n            return {\n                title: \"Infobox Title\",\n                backgroundColor: \"#f8f9fa\",\n                borderColor: \"#a2a9b1\"\n            };\n        default:\n            return {};\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (VisualCanvas);\nvar _c, _c1;\n$RefreshReg$(_c, \"VisualCanvas\");\n$RefreshReg$(_c1, \"CanvasElementComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/editor/visual-canvas.tsx\n"));

/***/ })

});