'use client'

import React, { useState, useEffect } from 'react'
import { X, ArrowRight, ArrowLeft, Lightbulb, MousePointer, <PERSON><PERSON>, Eye } from 'lucide-react'
import { useLanguage } from '@/contexts/language-context'

interface OnboardingStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  target?: string
  position?: 'center' | 'left' | 'right' | 'top' | 'bottom'
}

interface OnboardingGuideProps {
  isVisible: boolean
  onClose: () => void
  onComplete: () => void
}

export const OnboardingGuide: React.FC<OnboardingGuideProps> = ({
  isVisible,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0)
  const { t, language } = useLanguage()

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: language === 'zh' ? '欢迎使用 MediaWiki 模板构建器！' : 'Welcome to MediaWiki Template Builder!',
      description: language === 'zh' 
        ? '让我们快速了解如何使用这个强大的可视化模板编辑器。' 
        : 'Let\'s take a quick tour of this powerful visual template editor.',
      icon: <Lightbulb className="w-6 h-6" />,
      position: 'center'
    },
    {
      id: 'components',
      title: language === 'zh' ? '组件面板' : 'Component Palette',
      description: language === 'zh'
        ? '从左侧面板拖拽组件到画布上。我们提供了文本、图片、表格等多种组件。'
        : 'Drag components from the left panel onto the canvas. We provide text, images, tables, and more.',
      icon: <Palette className="w-6 h-6" />,
      target: '.component-palette',
      position: 'right'
    },
    {
      id: 'canvas',
      title: language === 'zh' ? '可视化画布' : 'Visual Canvas',
      description: language === 'zh'
        ? '在画布上直接编辑元素。点击选择，拖拽移动，调整大小和属性。'
        : 'Edit elements directly on the canvas. Click to select, drag to move, resize and adjust properties.',
      icon: <MousePointer className="w-6 h-6" />,
      target: '.visual-canvas',
      position: 'left'
    },
    {
      id: 'properties',
      title: language === 'zh' ? '属性面板' : 'Properties Panel',
      description: language === 'zh'
        ? '选择元素后，在右侧面板中编辑其属性、样式和生成的代码。'
        : 'After selecting an element, edit its properties, styles, and generated code in the right panel.',
      icon: <Eye className="w-6 h-6" />,
      target: '.properties-panel',
      position: 'left'
    },
    {
      id: 'templates',
      title: language === 'zh' ? '模板库' : 'Template Library',
      description: language === 'zh'
        ? '点击"浏览库"按钮访问预制模板，快速开始您的项目。'
        : 'Click the "Browse Library" button to access pre-made templates and jumpstart your project.',
      icon: <Palette className="w-6 h-6" />,
      position: 'center'
    },
    {
      id: 'complete',
      title: language === 'zh' ? '开始创建！' : 'Start Creating!',
      description: language === 'zh'
        ? '您已经准备好开始创建专业的 MediaWiki 模板了。祝您使用愉快！'
        : 'You\'re ready to start creating professional MediaWiki templates. Happy building!',
      icon: <ArrowRight className="w-6 h-6" />,
      position: 'center'
    }
  ]

  const currentStepData = steps[currentStep]

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSkip = () => {
    onClose()
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="bg-blue-100 p-2 rounded-lg mr-3">
              {currentStepData.icon}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {currentStepData.title}
              </h3>
              <p className="text-sm text-gray-500">
                {language === 'zh' ? `步骤 ${currentStep + 1} / ${steps.length}` : `Step ${currentStep + 1} of ${steps.length}`}
              </p>
            </div>
          </div>
          <button
            onClick={handleSkip}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="mb-6">
          <p className="text-gray-700 leading-relaxed">
            {currentStepData.description}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-xs text-gray-500 mb-2">
            <span>{language === 'zh' ? '进度' : 'Progress'}</span>
            <span>{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <div className="flex space-x-2">
            {currentStep > 0 && (
              <button
                onClick={handlePrevious}
                className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                {language === 'zh' ? '上一步' : 'Previous'}
              </button>
            )}
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={handleSkip}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              {language === 'zh' ? '跳过' : 'Skip'}
            </button>
            <button
              onClick={handleNext}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {currentStep === steps.length - 1 
                ? (language === 'zh' ? '完成' : 'Finish')
                : (language === 'zh' ? '下一步' : 'Next')
              }
              {currentStep < steps.length - 1 && <ArrowRight className="w-4 h-4 ml-1" />}
            </button>
          </div>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center mt-4 space-x-2">
          {steps.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentStep(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentStep ? 'bg-blue-600' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export default OnboardingGuide
