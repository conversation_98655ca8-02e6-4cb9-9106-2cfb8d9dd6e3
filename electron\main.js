const { app, BrowserWindow, <PERSON>u, dialog, ipc<PERSON>ain, shell } = require('electron')
const path = require('path')
const fs = require('fs')
const isDev = process.env.NODE_ENV === 'development'

let mainWindow

async function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false,
    titleBarStyle: 'default'
  })

  // Load the app
  if (isDev) {
    // Try different ports in case 3000 is occupied
    const tryLoadURL = async (port) => {
      try {
        console.log(`Attempting to load from port ${port}...`)
        await mainWindow.loadURL(`http://localhost:${port}`)
        console.log(`Successfully loaded app from port ${port}`)
        return true
      } catch (error) {
        console.log(`Failed to load from port ${port}: ${error.message}`)
        if (port < 3005) {
          // Wait a bit before trying next port
          await new Promise(resolve => setTimeout(resolve, 1000))
          return await tryLoadURL(port + 1)
        } else {
          console.error('Could not connect to development server on any port')
          // Fallback to a basic error page
          await mainWindow.loadURL('data:text/html,<h1>Development server not found</h1><p>Please make sure Next.js dev server is running with: npm run dev</p>')
          return false
        }
      }
    }

    await tryLoadURL(3000)
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../out/index.html'))
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    // Focus on window
    if (isDev) {
      mainWindow.focus()
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// App event handlers
app.whenReady().then(async () => {
  await createWindow()
  createMenu()

  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      await createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Template',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-template')
          }
        },
        {
          label: 'Open Template',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'Template Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            })
            
            if (!result.canceled && result.filePaths.length > 0) {
              try {
                const templateData = fs.readFileSync(result.filePaths[0], 'utf8')
                mainWindow.webContents.send('menu-open-template', JSON.parse(templateData))
              } catch (error) {
                dialog.showErrorBox('Error', 'Failed to open template file: ' + error.message)
              }
            }
          }
        },
        {
          label: 'Save Template',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('menu-save-template')
          }
        },
        { type: 'separator' },
        {
          label: 'Export MediaWiki Package',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.send('menu-export-package')
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About MediaWiki Template Builder',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About',
              message: 'MediaWiki Template Builder',
              detail: 'A visual tool for creating MediaWiki templates and CSS through drag-and-drop interface.\n\nVersion: 1.0.0\nBuilt with Electron and Next.js'
            })
          }
        },
        {
          label: 'Documentation',
          click: () => {
            shell.openExternal('https://github.com/your-repo/mediawiki-template-builder')
          }
        }
      ]
    }
  ]

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    })
  }

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// IPC handlers for file operations
ipcMain.handle('save-template-file', async (event, templateData) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      defaultPath: `${templateData.name || 'template'}.json`,
      filters: [
        { name: 'Template Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, JSON.stringify(templateData, null, 2))
      return { success: true, filePath: result.filePath }
    }
    
    return { success: false, canceled: true }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

ipcMain.handle('export-mediawiki-package', async (event, packageData) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      defaultPath: `${packageData.templateName || 'template'}-mediawiki-package.zip`,
      filters: [
        { name: 'ZIP Files', extensions: ['zip'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      // The ZIP creation will be handled by the renderer process
      // We just return the file path
      return { success: true, filePath: result.filePath }
    }
    
    return { success: false, canceled: true }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

ipcMain.handle('show-save-dialog', async (event, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, options)
    return result
  } catch (error) {
    return { canceled: true, error: error.message }
  }
})

ipcMain.handle('show-open-dialog', async (event, options) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, options)
    return result
  } catch (error) {
    return { canceled: true, error: error.message }
  }
})

// File storage handlers
ipcMain.handle('save-file', async (event, filename, content) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      defaultPath: filename,
      filters: [
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, content, 'utf8')
      return true
    }

    return false
  } catch (error) {
    console.error('Error saving file:', error)
    return false
  }
})

ipcMain.handle('load-file', async (event, filename) => {
  try {
    if (filename) {
      // Load specific file
      return fs.readFileSync(filename, 'utf8')
    } else {
      // Show open dialog
      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile'],
        filters: [
          { name: 'All Files', extensions: ['*'] }
        ]
      })

      if (!result.canceled && result.filePaths.length > 0) {
        return fs.readFileSync(result.filePaths[0], 'utf8')
      }
    }

    return null
  } catch (error) {
    console.error('Error loading file:', error)
    return null
  }
})

ipcMain.handle('delete-file', async (event, filename) => {
  try {
    if (fs.existsSync(filename)) {
      fs.unlinkSync(filename)
      return true
    }
    return false
  } catch (error) {
    console.error('Error deleting file:', error)
    return false
  }
})

ipcMain.handle('list-files', async (event, directory) => {
  try {
    const targetDir = directory || process.cwd()
    return fs.readdirSync(targetDir)
  } catch (error) {
    console.error('Error listing files:', error)
    return []
  }
})

ipcMain.handle('export-zip', async (event, filename, files) => {
  try {
    // For ZIP creation, we'll use a simple approach
    // In a production app, you'd want to use a proper ZIP library like 'archiver'
    const JSZip = require('jszip')
    const zip = new JSZip()

    // Add files to ZIP
    for (const [fileName, content] of Object.entries(files)) {
      zip.file(fileName, content)
    }

    // Generate ZIP buffer
    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' })

    // Write to file
    fs.writeFileSync(filename, zipBuffer)
    return true
  } catch (error) {
    console.error('Error creating ZIP:', error)
    return false
  }
})

// Handle app updates and notifications
ipcMain.handle('show-notification', async (event, title, body) => {
  const { Notification } = require('electron')

  if (Notification.isSupported()) {
    const notification = new Notification({
      title,
      body,
      icon: path.join(__dirname, '../assets/icon.png')
    })

    notification.show()
    return { success: true }
  }

  return { success: false, reason: 'Notifications not supported' }
})

// Prevent navigation away from the app
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (navigationEvent, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    // Allow localhost on any port and file:// protocol
    const isLocalhost = parsedUrl.hostname === 'localhost'
    const isFile = parsedUrl.protocol === 'file:'

    if (!isLocalhost && !isFile) {
      navigationEvent.preventDefault()
    }
  })
})
